// 模拟项目数据
export const mockProjects = [
  {
    id: 1,
    name: '某住宅小区A区工程',
    code: 'SJZ2025001',
    type: '预算',
    businessType: '预算',
    region: { province: '河北省', city: '石家庄市' },
    constructionUnit: '石家庄市住建局',
    createTime: '2024-01-15 14:30',
    updateTime: '2024-01-15 16:20',
    location: 'C:\\项目\\某住宅小区A区工程.rcj',
    status: '编制中',
    totalAmount: 2850.5,
    totalItems: 1200,
    standards: {
      listStandard: 'GB50500-2013',
      quotaStandard: '河北22定额',
      pricingMode: '全费用单价'
    }
  },
  {
    id: 2,
    name: '某商业综合体B栋工程',
    code: 'BD2025002',
    type: '概算',
    businessType: '概算',
    region: { province: '河北省', city: '保定市' },
    constructionUnit: '保定市城投集团',
    createTime: '2024-01-14 16:20',
    updateTime: '2024-01-14 18:45',
    location: 'C:\\项目\\某商业综合体B栋工程.dgc',
    status: '已完成',
    totalAmount: 5200.8,
    totalItems: 2100,
    standards: {
      listStandard: 'GB50500-2013',
      quotaStandard: '河北22定额',
      pricingMode: '清单计价'
    }
  },
  {
    id: 3,
    name: '某办公楼装修工程',
    code: 'TS2025003',
    type: '结算',
    businessType: '结算',
    region: { province: '河北省', city: '唐山市' },
    constructionUnit: '唐山市政府',
    createTime: '2024-01-13 09:45',
    updateTime: '2024-01-13 15:30',
    location: 'C:\\项目\\某办公楼装修工程.gcxm',
    status: '审核中',
    totalAmount: 1680.2,
    totalItems: 850,
    standards: {
      listStandard: 'GB50500-2024',
      quotaStandard: '河北24定额',
      pricingMode: '全费用单价'
    }
  },
  {
    id: 4,
    name: '某学校教学楼工程',
    code: 'HD2025004',
    type: '审核',
    businessType: '审核',
    region: { province: '河北省', city: '邯郸市' },
    constructionUnit: '邯郸市教育局',
    createTime: '2024-01-12 11:15',
    updateTime: '2024-01-12 17:20',
    location: 'C:\\项目\\某学校教学楼工程.sh',
    status: '审核完成',
    totalAmount: 3200.6,
    totalItems: 1500,
    standards: {
      listStandard: 'GB50500-2013',
      quotaStandard: '河北22定额',
      pricingMode: '清单计价'
    }
  },
  {
    id: 5,
    name: '某医院门诊楼工程',
    code: 'SJZ2025005',
    type: '预算',
    businessType: '预算',
    region: { province: '河北省', city: '石家庄市' },
    constructionUnit: '石家庄市卫健委',
    createTime: '2024-01-11 15:30',
    updateTime: '2024-01-11 17:45',
    location: 'C:\\项目\\某医院门诊楼工程.rcj',
    status: '编制中',
    totalAmount: 4500.3,
    totalItems: 1800,
    standards: {
      listStandard: 'GB50500-2024',
      quotaStandard: '河北24定额',
      pricingMode: '全费用单价'
    }
  }
]

// 工程结构树数据
export const mockProjectStructure = {
  id: 1,
  projectId: 1,
  structure: [
    {
      key: 'project',
      title: '某住宅小区A区工程',
      type: 'project',
      itemCount: 1200,
      amount: 2850.5,
      children: [
        {
          key: 'item1',
          title: '1# 住宅楼',
          type: 'item',
          itemCount: 600,
          amount: 1425.3,
          children: [
            {
              key: 'unit1',
              title: '1#楼主体结构',
              type: 'unit',
              itemCount: 300,
              amount: 850.2,
              subdivisions: [
                {
                  id: 1,
                  code: '',
                  type: '单位',
                  name: '1#楼主体结构',
                  characteristics: '5层砖混结构',
                  unit: '',
                  quantity: null,
                  unitPrice: null,
                  totalPrice: 8502000,
                  level: 0
                },
                {
                  id: 2,
                  code: '0101',
                  type: '分部',
                  name: '土石方工程',
                  characteristics: '',
                  unit: '',
                  quantity: null,
                  unitPrice: null,
                  totalPrice: 102000,
                  level: 1
                },
                {
                  id: 3,
                  code: '010101001',
                  type: '清单',
                  name: '平整场地',
                  characteristics: '机械平整',
                  unit: 'm²',
                  quantity: 1200,
                  unitPrice: 8.5,
                  totalPrice: 10200,
                  level: 2
                },
                {
                  id: 4,
                  code: 'A1-1',
                  type: '定额',
                  name: '推土机平整场地',
                  characteristics: '功率59kW',
                  unit: '100m²',
                  quantity: 12,
                  unitPrice: 85,
                  totalPrice: 1020,
                  level: 3
                },
                {
                  id: 5,
                  code: 'A1-2',
                  type: '定额',
                  name: '人工修整场地',
                  characteristics: '人工精平',
                  unit: 'm²',
                  quantity: 1200,
                  unitPrice: 6.5,
                  totalPrice: 7800,
                  level: 3
                },
                {
                  id: 6,
                  code: '010102001',
                  type: '清单',
                  name: '挖基础土方',
                  characteristics: '一般土壤',
                  unit: 'm³',
                  quantity: 800,
                  unitPrice: 45.5,
                  totalPrice: 36400,
                  level: 2
                },
                {
                  id: 7,
                  code: 'A2-1',
                  type: '定额',
                  name: '挖掘机挖土',
                  characteristics: '斗容量1m³',
                  unit: 'm³',
                  quantity: 800,
                  unitPrice: 35.2,
                  totalPrice: 28160,
                  level: 3
                },
                {
                  id: 8,
                  code: 'A2-2',
                  type: '定额',
                  name: '人工修整基底',
                  characteristics: '人工修整',
                  unit: 'm²',
                  quantity: 200,
                  unitPrice: 12.5,
                  totalPrice: 2500,
                  level: 3
                }
              ]
            },
            {
              key: 'unit2',
              title: '1#楼装饰装修',
              type: 'unit',
              itemCount: 300,
              amount: 575.1,
              subdivisions: []
            }
          ]
        },
        {
          key: 'item2',
          title: '2# 住宅楼',
          type: 'item',
          itemCount: 600,
          amount: 1425.2,
          children: [
            {
              key: 'unit3',
              title: '2#楼主体结构',
              type: 'unit',
              itemCount: 300,
              amount: 850.1,
              subdivisions: []
            },
            {
              key: 'unit4',
              title: '2#楼装饰装修',
              type: 'unit',
              itemCount: 300,
              amount: 575.1,
              subdivisions: []
            }
          ]
        }
      ]
    }
  ]
}

// 人材机数据
export const mockMaterialsData = [
  {
    id: 1,
    quotaId: 4, // 对应定额A1-1
    materials: [
      {
        id: 1,
        code: 'RG001',
        type: '人工',
        name: '普通工',
        specification: '',
        unit: '工日',
        consumption: 0.1,
        unitPrice: 120,
        totalPrice: 12
      },
      {
        id: 2,
        code: 'JX001',
        type: '机械',
        name: '推土机',
        specification: '功率59kW',
        unit: '台班',
        consumption: 0.05,
        unitPrice: 800,
        totalPrice: 40
      }
    ]
  },
  {
    id: 2,
    quotaId: 5, // 对应定额A1-2
    materials: [
      {
        id: 3,
        code: 'RG001',
        type: '人工',
        name: '普通工',
        specification: '',
        unit: '工日',
        consumption: 0.05,
        unitPrice: 120,
        totalPrice: 6
      }
    ]
  },
  {
    id: 3,
    quotaId: 7, // 对应定额A2-1
    materials: [
      {
        id: 4,
        code: 'RG002',
        type: '人工',
        name: '机械操作工',
        specification: '',
        unit: '工日',
        consumption: 0.08,
        unitPrice: 150,
        totalPrice: 12
      },
      {
        id: 5,
        code: 'JX002',
        type: '机械',
        name: '挖掘机',
        specification: '斗容量1m³',
        unit: '台班',
        consumption: 0.12,
        unitPrice: 1200,
        totalPrice: 144
      },
      {
        id: 6,
        code: 'CL001',
        type: '材料',
        name: '柴油',
        specification: '0#',
        unit: 'kg',
        consumption: 25,
        unitPrice: 6.8,
        totalPrice: 170
      }
    ]
  }
]

// 标准库数据
export const mockListLibrary = [
  {
    id: 1,
    code: '010101001',
    name: '平整场地',
    unit: 'm²',
    category: '土石方工程',
    characteristics: '机械平整',
    workContent: '推土机平整、人工修整',
    updateTime: '2024-01-15'
  },
  {
    id: 2,
    code: '010101002',
    name: '挖基础土方',
    unit: 'm³',
    category: '土石方工程',
    characteristics: '一般土壤',
    workContent: '挖掘机挖土、装车外运',
    updateTime: '2024-01-15'
  },
  {
    id: 3,
    code: '010102001',
    name: '挖沟槽土方',
    unit: 'm³',
    category: '土石方工程',
    characteristics: '深度2m以内',
    workContent: '机械挖土、人工配合',
    updateTime: '2024-01-15'
  },
  {
    id: 4,
    code: '020101001',
    name: '砖基础',
    unit: 'm³',
    category: '砌筑工程',
    characteristics: 'MU10机制砖',
    workContent: '砌筑、勾缝',
    updateTime: '2024-01-15'
  },
  {
    id: 5,
    code: '020201001',
    name: '现浇混凝土基础',
    unit: 'm³',
    category: '混凝土工程',
    characteristics: 'C25混凝土',
    workContent: '浇筑、振捣、养护',
    updateTime: '2024-01-15'
  }
]

export const mockQuotaLibrary = [
  {
    id: 1,
    code: 'A1-1',
    name: '推土机平整场地',
    unit: '100m²',
    category: 'A 土石方工程',
    unitPrice: 85.50,
    characteristics: '功率59kW',
    workContent: '推土机平整场地',
    updateTime: '2024-01-15',
    materials: [
      { type: '人工', name: '普通工', unit: '工日', consumption: 0.1 },
      { type: '机械', name: '推土机', unit: '台班', consumption: 0.05 }
    ]
  },
  {
    id: 2,
    code: 'A1-2',
    name: '人工平整场地',
    unit: 'm²',
    category: 'A 土石方工程',
    unitPrice: 6.50,
    characteristics: '人工精平',
    workContent: '人工平整场地',
    updateTime: '2024-01-15',
    materials: [
      { type: '人工', name: '普通工', unit: '工日', consumption: 0.05 }
    ]
  },
  {
    id: 3,
    code: 'A2-1',
    name: '挖掘机挖土',
    unit: 'm³',
    category: 'A 土石方工程',
    unitPrice: 35.20,
    characteristics: '斗容量1m³',
    workContent: '挖掘机挖土',
    updateTime: '2024-01-15',
    materials: [
      { type: '人工', name: '机械操作工', unit: '工日', consumption: 0.08 },
      { type: '机械', name: '挖掘机', unit: '台班', consumption: 0.12 },
      { type: '材料', name: '柴油', unit: 'kg', consumption: 25 }
    ]
  },
  {
    id: 4,
    code: 'A2-2',
    name: '人工修整基底',
    unit: 'm²',
    category: 'A 土石方工程',
    unitPrice: 12.50,
    characteristics: '人工修整',
    workContent: '人工修整基底',
    updateTime: '2024-01-15',
    materials: [
      { type: '人工', name: '普通工', unit: '工日', consumption: 0.08 }
    ]
  },
  {
    id: 5,
    code: 'B1-1',
    name: '砖基础砌筑',
    unit: 'm³',
    category: 'B 砌筑工程',
    unitPrice: 285.60,
    characteristics: 'MU10机制砖',
    workContent: '砌筑、勾缝',
    updateTime: '2024-01-15',
    materials: [
      { type: '人工', name: '瓦工', unit: '工日', consumption: 1.2 },
      { type: '材料', name: '机制砖', unit: '千块', consumption: 0.53 },
      { type: '材料', name: '水泥砂浆', unit: 'm³', consumption: 0.25 }
    ]
  }
]

// 费率数据
export const mockRateData = {
  managementFee: 8.5, // 企业管理费率
  profit: 7.0,        // 利润率
  regulatoryFee: 3.2, // 规费费率
  tax: 9.0            // 税金费率
}

// 地区信息
export const mockRegionData = {
  provinces: [
    {
      name: '河北省',
      cities: [
        '石家庄市', '唐山市', '秦皇岛市', '邯郸市', '邢台市', 
        '保定市', '张家口市', '承德市', '沧州市', '廊坊市', '衡水市'
      ]
    },
    {
      name: '北京市',
      cities: [
        '东城区', '西城区', '朝阳区', '丰台区', '石景山区', '海淀区',
        '门头沟区', '房山区', '通州区', '顺义区', '昌平区', '大兴区',
        '怀柔区', '平谷区', '密云区', '延庆区'
      ]
    },
    {
      name: '天津市',
      cities: [
        '和平区', '河东区', '河西区', '南开区', '河北区', '红桥区',
        '东丽区', '西青区', '津南区', '北辰区', '武清区', '宝坻区',
        '滨海新区', '宁河区', '静海区', '蓟州区'
      ]
    }
  ]
}
