<template>
  <div class="dashboard">
    <a-layout class="layout">
      <!-- 使用共享头部组件 -->
      <AppHeader
        class="dashboard-header"
        title="造价管理系统"
        :show-window-controls="true"
        :is-maximized="isMaximized"
        @minimize="minimizeWindow"
        @maximize="toggleMaximize"
        @close="closeWindow"
      >
        <template #right>
          <div class="user-info">
            <a-space>
              <a-avatar :size="32" style="background-color: rgba(255, 255, 255, 0.2); color: white;">
                <template #icon><UserOutlined /></template>
              </a-avatar>
              <span style="color: white; font-weight: 500;">管理员</span>
            </a-space>
          </div>
        </template>
      </AppHeader>

      <!-- 主内容区 -->
      <a-layout-content class="content">
        <div class="content-wrapper">
          <!-- 地区选择区域 -->
          <a-card title="地区选择区域" class="region-selector-card">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="省份：">
                  <a-select
                    v-model:value="selectedProvince"
                    placeholder="河北省"
                    @change="onProvinceChange"
                  >
                    <a-select-option value="河北省">河北省</a-select-option>
                    <a-select-option value="北京市">北京市</a-select-option>
                    <a-select-option value="天津市">天津市</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="地市：">
                  <a-select
                    v-model:value="selectedCity"
                    placeholder="选择"
                    :disabled="!selectedProvince"
                  >
                    <a-select-option
                      v-for="city in availableCities"
                      :key="city"
                      :value="city"
                    >
                      {{ city }}
                    </a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
          </a-card>

          <!-- 业务类型选择 -->
          <a-card title="业务类型选择" class="business-type-card">
            <a-radio-group v-model:value="selectedBusinessType" @change="onBusinessTypeChange">
              <a-radio value="概算">概算</a-radio>
              <a-radio value="预算" checked>预算</a-radio>
              <a-radio value="结算">结算</a-radio>
              <a-radio value="审核">审核</a-radio>
            </a-radio-group>
          </a-card>

          <!-- 最近项目列表 -->
          <a-card title="最近项目列表" class="recent-projects-card">
            <div class="recent-files">
              <a-list :data-source="recentFiles" size="small">
                <template #renderItem="{ item }">
                  <a-list-item class="file-item" @click="openFileInModule(item)">
                    <a-list-item-meta
                      :title="item.name"
                      :description="`${item.type} | ${item.updateTime}`"
                    >
                      <template #avatar>
                        <a-avatar :style="{ backgroundColor: getFileTypeColor(item.type) }">
                          <template #icon>
                            <FolderOutlined />
                          </template>
                        </a-avatar>
                      </template>
                    </a-list-item-meta>
                  </a-list-item>
                </template>
              </a-list>
            </div>
          </a-card>

          <!-- 操作按钮区域 -->
          <div class="action-buttons">
            <a-space size="large">
              <a-button
                type="primary"
                size="large"
                @click="showCreateProjectModal"
                :disabled="!selectedBusinessType"
              >
                <template #icon><PlusOutlined /></template>
                {{ getNewButtonText() }}
              </a-button>
              <a-button size="large" @click="openFile">
                <template #icon><FolderOpenOutlined /></template>
                打开项目
              </a-button>
              <a-button size="large" @click="showSettings">
                <template #icon><SettingOutlined /></template>
                设置
              </a-button>
            </a-space>
          </div>
        </div>
      </a-layout-content>
    </a-layout>

    <!-- 项目创建向导模态框 -->
    <ProjectCreateWizard
      v-model:visible="createProjectVisible"
      :business-type="selectedBusinessType"
      :region="{ province: selectedProvince, city: selectedCity }"
      @success="onProjectCreated"
    />
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { invoke } from '@tauri-apps/api/core'
import { AppHeader, useMainWindowManagement } from '@cost-app/shared-components'
import ProjectCreateWizard from '../components/ProjectCreateWizard.vue'
import {
  UserOutlined,
  CalculatorOutlined,
  DollarOutlined,
  CheckCircleOutlined,
  FileTextOutlined,
  AuditOutlined,
  FolderOutlined,
  PlusOutlined,
  FolderOpenOutlined,
  SettingOutlined
} from '@ant-design/icons-vue'
// 使用新的简化窗口管理
const {
  isMaximized,
  minimizeWindow: minimize,
  toggleMaximize: toggle,
  closeWindow: close
} = useMainWindowManagement()

// 包装窗口操作函数以添加用户反馈
const minimizeWindow = async () => {
  try {
    await minimize()
    message.success('窗口已最小化')
  } catch (error) {
    message.error('窗口操作失败')
  }
}

const toggleMaximize = async () => {
  try {
    await toggle()
  } catch (error) {
    message.error('窗口操作失败')
  }
}

const closeWindow = async () => {
  try {
    await close()
  } catch (error) {
    message.error('窗口操作失败')
  }
}

// 地区选择数据
const selectedProvince = ref('河北省')
const selectedCity = ref('')

const cityMap = {
  '河北省': ['石家庄市', '唐山市', '秦皇岛市', '邯郸市', '邢台市', '保定市', '张家口市', '承德市', '沧州市', '廊坊市', '衡水市'],
  '北京市': ['东城区', '西城区', '朝阳区', '丰台区', '石景山区', '海淀区', '门头沟区', '房山区', '通州区', '顺义区', '昌平区', '大兴区', '怀柔区', '平谷区', '密云区', '延庆区'],
  '天津市': ['和平区', '河东区', '河西区', '南开区', '河北区', '红桥区', '东丽区', '西青区', '津南区', '北辰区', '武清区', '宝坻区', '滨海新区', '宁河区', '静海区', '蓟州区']
}

const availableCities = computed(() => {
  return cityMap[selectedProvince.value] || []
})

// 业务类型选择
const selectedBusinessType = ref('预算')

// 项目创建向导模态框状态
const createProjectVisible = ref(false)

// 最近项目列表数据
const recentFiles = ref([
  {
    id: 1,
    name: '某住宅项目_SJZ2025001',
    type: '预算',
    updateTime: '2024-01-15 14:30',
    location: 'C:\\项目\\某住宅项目.rcj'
  },
  {
    id: 2,
    name: '某商业项目_BD2025002',
    type: '概算',
    updateTime: '2024-01-14 16:20',
    location: 'C:\\项目\\某商业项目.dgc'
  },
  {
    id: 3,
    name: '某办公楼项目_TS2025003',
    type: '结算',
    updateTime: '2024-01-13 09:45',
    location: 'C:\\项目\\某办公楼项目.gcxm'
  },
  {
    id: 4,
    name: '某学校项目_HD2025004',
    type: '审核',
    updateTime: '2024-01-12 11:15',
    location: 'C:\\项目\\某学校项目.sh'
  },
  {
    id: 5,
    name: '某医院项目_SJZ2025005',
    type: '预算',
    updateTime: '2024-01-11 15:30',
    location: 'C:\\项目\\某医院项目.rcj'
  }
])

const modules = ref([
  {
    key: 'rough-estimate',
    title: '概算',
    description: '项目概算管理和计算',
    icon: CalculatorOutlined,
    color: '#1890ff',
    status: 'ready',
    port: 5174
  },
  {
    key: 'budget',
    title: '预算',
    description: '项目预算编制和管理',
    icon: DollarOutlined,
    color: '#52c41a',
    status: 'development',
    port: 5175
  },
  {
    key: 'budget-review',
    title: '预算审核',
    description: '预算审核流程管理',
    icon: CheckCircleOutlined,
    color: '#faad14',
    status: 'development',
    port: 5176
  },
  {
    key: 'settlement',
    title: '结算',
    description: '项目结算管理',
    icon: FileTextOutlined,
    color: '#722ed1',
    status: 'development',
    port: 5177
  },
  {
    key: 'settlement-review',
    title: '结算审核',
    description: '结算审核流程管理',
    icon: AuditOutlined,
    color: '#eb2f96',
    status: 'development',
    port: 5178
  },
])

// 地区选择相关方法
const onProvinceChange = (value) => {
  selectedCity.value = '' // 清空城市选择
  message.info(`已选择省份：${value}`)
}

// 业务类型选择相关方法
const onBusinessTypeChange = (e) => {
  const value = e.target.value
  message.info(`已选择业务类型：${value}`)
}

// 获取新建按钮文字
const getNewButtonText = () => {
  if (!selectedBusinessType.value) return '新建项目'
  return `新建${selectedBusinessType.value}`
}

// 获取文件类型颜色
const getFileTypeColor = (type) => {
  const colorMap = {
    '概算': '#1890ff',
    '预算': '#52c41a',
    '结算': '#722ed1',
    '审核': '#faad14'
  }
  return colorMap[type] || '#1890ff'
}

// 打开文件到对应模块
const openFileInModule = (file) => {
  const moduleMap = {
    '概算': 'rough-estimate',
    '预算': 'budget',
    '结算': 'settlement',
    '审核': 'budget-review'
  }

  const moduleKey = moduleMap[file.type]
  if (moduleKey) {
    const module = modules.value.find(m => m.key === moduleKey)
    if (module) {
      openModule(module, file)
    }
  } else {
    message.warning(`暂不支持打开${file.type}类型文件`)
  }
}

// 显示项目创建向导
const showCreateProjectModal = () => {
  if (!selectedBusinessType.value) {
    message.warning('请先选择业务类型')
    return
  }

  if (!selectedProvince.value || !selectedCity.value) {
    message.warning('请先选择地区')
    return
  }

  createProjectVisible.value = true
}

// 项目创建成功回调
const onProjectCreated = (projectData) => {
  message.success('项目创建成功！')

  // 添加到最近项目列表
  const newProject = {
    id: Date.now(),
    name: projectData.projectName,
    type: selectedBusinessType.value,
    updateTime: new Date().toLocaleString(),
    location: `C:\\项目\\${projectData.projectCode}${projectData.projectName}.${getFileExtension(selectedBusinessType.value)}`
  }

  recentFiles.value.unshift(newProject)
  if (recentFiles.value.length > 5) {
    recentFiles.value.pop()
  }

  // 自动打开对应的业务模块
  const moduleMap = {
    '概算': 'rough-estimate',
    '预算': 'budget',
    '结算': 'settlement',
    '审核': 'budget-review'
  }

  const moduleKey = moduleMap[selectedBusinessType.value]
  if (moduleKey) {
    const module = modules.value.find(m => m.key === moduleKey)
    if (module) {
      setTimeout(() => {
        openModule(module, newProject)
      }, 1000)
    }
  }
}

// 获取文件扩展名
const getFileExtension = (businessType) => {
  const extensionMap = {
    '概算': 'dgc',
    '预算': 'rcj',
    '结算': 'gcxm',
    '审核': 'sh'
  }
  return extensionMap[businessType] || 'prj'
}

// 打开文件
const openFile = () => {
  message.info('打开文件功能开发中...')
  // TODO: 实现文件打开功能
}

// 显示设置
const showSettings = () => {
  message.info('设置功能开发中...')
  // TODO: 实现设置功能
}

const openModule = async (module, file = null) => {
  try {
    message.loading(`正在打开${module.title}模块...`, 2)

    // 对于预算模块，直接跳转到工作台页面
    if (module.key === 'budget') {
      // 使用路由跳转到工作台
      await router.push({
        name: 'WorkbenchMain',
        query: file ? {
          fileId: file.id,
          fileName: file.name,
          fileType: file.type
        } : {}
      })
      message.success(`${module.title}模块已打开`)
      return
    }

    // 其他模块：使用开发服务器或静态文件
    const isDev = import.meta.env.DEV
    let url

    if (isDev) {
      // 开发环境：使用开发服务器端口
      url = `http://localhost:${module.port}`
    } else {
      // 生产环境：使用相对路径访问打包后的静态文件
      url = `${module.key}/index.html`
    }

    // 如果有文件参数，添加到URL中
    if (file) {
      const params = new URLSearchParams({
        fileId: file.id,
        fileName: file.name,
        fileType: file.type
      })
      url += `?${params.toString()}`
    }

    // 调用 Tauri 命令创建新窗口
    await invoke('create_module_window', {
      moduleKey: module.key,
      title: file ? `${module.title} - ${file.name}` : module.title,
      url: url
    })

    message.success(`${module.title}模块已在新窗口中打开`)
  } catch (error) {
    console.error('打开模块失败:', error)
    message.error(`打开${module.title}模块失败: ${error}`)
  }
}

// 路由实例
const router = useRouter()

// 窗口管理通过 useMainWindowManagement 自动初始化
</script>

<style scoped>
.dashboard {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  padding: 0;
  border-bottom: none;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  width: 100%;
  padding: 0 24px;
}

.header-center {
  flex: 1;
  height: 100%;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.dashboard-header {
  flex-shrink: 0;
  height: 64px;
}

.logo h2 {
  margin: 0;
  color: white;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.content {
  flex: 1;
  background: #f0f2f5;
  padding: 24px;
  overflow-y: auto;
  overflow-x: hidden;
}

.content-wrapper {
  max-width: 1200px;
  margin: 0 auto;
}

/* 地区选择卡片 */
.region-selector-card {
  margin-bottom: 24px;
}

.region-selector-card :deep(.ant-form-item) {
  margin-bottom: 0;
}

.region-selector-card :deep(.ant-form-item-label) {
  font-weight: 500;
}

/* 业务类型选择卡片 */
.business-type-card {
  margin-bottom: 24px;
}

.business-type-card :deep(.ant-radio-group) {
  display: flex;
  gap: 24px;
}

.business-type-card :deep(.ant-radio-wrapper) {
  font-size: 16px;
  font-weight: 500;
}

/* 最近项目列表卡片 */
.recent-projects-card {
  margin-bottom: 24px;
}

.file-item {
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-radius: 6px;
  padding: 8px;
  margin: 4px 0;
}

.file-item:hover {
  background-color: #f5f5f5;
}

/* 操作按钮区域 */
.action-buttons {
  text-align: center;
  margin-top: 32px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.modules-grid {
  margin-bottom: 24px;
}

.module-card {
  height: 200px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.module-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.module-content {
  text-align: center;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.module-icon {
  margin-bottom: 16px;
}

.module-content h3 {
  margin: 8px 0;
  color: #262626;
  font-size: 18px;
}

.module-content p {
  color: #8c8c8c;
  margin-bottom: 12px;
  font-size: 14px;
}

.status-card {
  background: white;
}

/* 窗口控制按钮样式 */
.window-controls {
  display: flex;
  align-items: center;
  gap: 4px;
}

.window-control-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.window-control-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.window-control-btn.close-btn:hover {
  background-color: #ff4d4f;
  color: white;
}

.window-control-btn {
  color: white;
}

/* 拖动区域样式 */
[data-tauri-drag-region] {
  -webkit-app-region: drag;
}

/* 确保按钮不被拖动 */
.window-controls,
.user-info,
.logo {
  -webkit-app-region: no-drag;
}
</style>
