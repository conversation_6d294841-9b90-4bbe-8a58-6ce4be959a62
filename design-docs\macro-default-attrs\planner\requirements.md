# ModuForge-RS Default 属性扩展 - 详细功能需求列表

## 1. 需求分析概述

### 1.1 项目背景分析
基于对架构指导文件的深入分析，本项目需要在现有 `moduforge-macros-derive` 库的基础上，为 Node 和 Mark 宏添加默认值支持。这是一个典型的**渐进式功能扩展**项目，必须确保向后兼容性和类型安全性。

### 1.2 核心价值分析
- **开发效率提升**：通过声明式默认值减少 60-80% 的样板代码
- **类型安全保障**：编译时验证确保默认值与字段类型完全匹配
- **开发体验改善**：友好的错误消息和修复建议
- **向后兼容保证**：现有代码无需修改即可继续工作

### 1.3 技术约束理解
- **实现约束**：只能在现有 `moduforge-macros-derive` 库中扩展
- **架构约束**：必须遵循现有的模块化设计和依赖原则
- **性能约束**：编译时间增加 < 10%，内存使用增加 < 20MB
- **兼容性约束**：与 `macro-extensions` 项目完全兼容

## 2. 功能需求详细分解

### 2.1 核心功能需求

#### 需求 REQ-001: 基础默认值语法支持
**优先级**: P0 (必须实现)
**复杂度**: 中等
**依赖关系**: 无前置依赖

**功能描述**:
支持在字段属性中声明默认值，语法为 `#[attr(default="value")]`

**用户故事**:
作为 ModuForge-RS 开发者，我希望能够为 Node 和 Mark 的字段声明默认值，这样我就不需要手写默认值设置逻辑，能够专注于业务逻辑开发。

**详细功能要求**:
1. **语法支持**：
   ```rust
   #[derive(Node, Serialize, Deserialize)]
   #[node_type = "paragraph"]
   pub struct Paragraph {
       #[attr(default = "默认内容")]
       content: String,
       
       #[attr(default = "16")]
       font_size: i32,
       
       #[attr]  // 现有语法继续支持
       author: Option<String>,
   }
   ```

2. **兼容性要求**：
   - 现有 `#[attr]` 语法完全保持不变
   - 新的 `default` 参数为可选扩展
   - 混合使用新旧语法

3. **支持范围**：
   - Node 派生宏的所有 `#[attr]` 字段
   - Mark 派生宏的所有 `#[attr]` 字段
   - 嵌套结构体中的属性字段

**验收标准**:
- [ ] 解析器能够正确识别 `default` 参数
- [ ] 现有代码不受任何影响继续编译通过
- [ ] 错误的语法格式能被正确捕获并报告
- [ ] 支持字符串、数值、布尔值的默认值声明

**技术实现点**:
- 扩展 `AttributeParser::parse_field_attributes` 方法
- 在 `FieldConfig` 中添加 `default_value: Option<DefaultValue>` 字段
- 保持现有解析逻辑不变，仅添加额外的默认值解析

#### 需求 REQ-002: 简单类型默认值支持
**优先级**: P0 (必须实现)
**复杂度**: 中等
**依赖关系**: 依赖 REQ-001

**功能描述**:
支持 Rust 基本数据类型的默认值声明和类型验证

**用户故事**:
作为开发者，我希望能够为常用的基本类型字段设置默认值，系统能够自动验证类型匹配，避免运行时错误。

**详细功能要求**:
1. **支持的类型列表**：
   ```rust
   // 字符串类型
   #[attr(default = "hello world")]
   text: String,
   
   // 整数类型
   #[attr(default = "42")]
   count: i32,
   
   #[attr(default = "9223372036854775807")]
   big_number: i64,
   
   // 浮点数类型
   #[attr(default = "3.14159")]
   pi: f64,
   
   #[attr(default = "2.5")]
   ratio: f32,
   
   // 布尔类型
   #[attr(default = "true")]
   enabled: bool,
   
   #[attr(default = "false")]
   disabled: bool,
   ```

2. **类型验证规则**：
   - String/&str 字段 → 任何字符串字面量
   - 整数字段 → 有效的数字字符串，范围检查
   - 浮点数字段 → 有效的浮点数字符串
   - 布尔字段 → "true" 或 "false" 字符串

3. **编译时验证**：
   - 默认值格式正确性检查
   - 默认值与字段类型的兼容性检查
   - 数值范围合理性检查

**验收标准**:
- [ ] 所有基本类型都能正确解析和验证默认值
- [ ] 类型不匹配时产生清晰的编译错误
- [ ] 数值越界时产生友好的错误提示
- [ ] 生成的代码中默认值被正确转换为目标类型

**技术实现点**:
- 实现 `DefaultValueValidator` trait 的各种类型验证器
- `SimpleTypeValidator` 处理基本类型验证
- `NumericRangeValidator` 处理数值范围检查
- 类型解析使用 `syn::Type` 分析字段类型

#### 需求 REQ-003: JSON 格式默认值支持
**优先级**: P1 (重要实现)
**复杂度**: 高
**依赖关系**: 依赖 REQ-001, REQ-002

**功能描述**:
支持 JSON 格式的复杂默认值，专门用于 `serde_json::Value` 类型字段

**用户故事**:
作为开发者，我希望能够为复杂的配置字段设置 JSON 格式的默认值，这样我可以在编译时定义复杂的数据结构作为默认配置。

**详细功能要求**:
1. **JSON 语法支持**：
   ```rust
   #[derive(Node)]
   #[node_type = "config"]
   pub struct ConfigNode {
       // 简单 JSON 对象
       #[attr(default = r#"{"theme": "dark", "size": 12}"#)]
       settings: serde_json::Value,
       
       // JSON 数组
       #[attr(default = r#"["option1", "option2", "option3"]"#)]
       options: serde_json::Value,
       
       // 复杂嵌套 JSON
       #[attr(default = r#"{
           "user": {
               "name": "默认用户",
               "preferences": {
                   "language": "zh-CN",
                   "notifications": true
               }
           }
       }"#)]
       user_config: serde_json::Value,
   }
   ```

2. **类型约束**：
   - JSON 格式默认值**只能**用于 `serde_json::Value` 类型字段
   - 其他类型字段使用 JSON 格式将产生编译错误
   - 支持 `Option<serde_json::Value>` 类型

3. **编译时 JSON 验证**：
   - JSON 语法正确性检查
   - JSON 格式化和美化
   - 无效 JSON 的友好错误消息

**验收标准**:
- [ ] 能够解析和验证各种复杂的 JSON 格式
- [ ] JSON 语法错误时提供精确的错误位置
- [ ] 非 `serde_json::Value` 类型使用 JSON 时报错
- [ ] 生成的代码中 JSON 被正确解析为 `serde_json::Value`

**技术实现点**:
- `JsonValidator` 实现 JSON 格式验证
- 使用 `serde_json::from_str` 进行编译时验证
- 特殊的错误类型 `InvalidJsonDefaultValue` 和 `JsonValueTypeRequired`
- 生成代码使用 `serde_json::json!` 宏

#### 需求 REQ-004: Option 类型默认值支持
**优先级**: P1 (重要实现)
**复杂度**: 中等
**依赖关系**: 依赖 REQ-001, REQ-002

**功能描述**:
支持 `Option<T>` 类型字段的默认值设置，包括 None 和 Some(value) 两种情况

**用户故事**:
作为开发者，我希望能够为可选字段设置默认值，既可以设置为 None，也可以设置为包含具体值的 Some。

**详细功能要求**:
1. **Option 默认值语法**：
   ```rust
   #[derive(Node)]
   #[node_type = "article"]
   pub struct Article {
       // None 默认值
       #[attr(default = "null")]
       author: Option<String>,
       
       // Some(value) 默认值
       #[attr(default = "未命名")]
       title: Option<String>,
       
       #[attr(default = "0")]
       view_count: Option<i32>,
       
       #[attr(default = "true")]
       published: Option<bool>,
   }
   ```

2. **验证规则**：
   - "null" 字符串表示 None 值
   - 其他字符串按照内部类型 T 的规则验证
   - 内部类型必须是支持的简单类型或 `serde_json::Value`

3. **类型推断**：
   - 自动提取 Option 的内部类型进行验证
   - 支持嵌套 Option（如 `Option<Option<String>>`）

**验收标准**:
- [ ] "null" 默认值正确生成 None
- [ ] 其他默认值正确生成 Some(value)
- [ ] 内部类型验证遵循相同的类型规则
- [ ] 嵌套 Option 类型得到正确处理

**技术实现点**:
- `OptionTypeValidator` 处理 Option 类型验证
- 类型解析器识别 `Option<T>` 并提取内部类型 T
- 特殊处理 "null" 字符串字面量
- 生成代码区分 None 和 Some(value) 情况

### 2.2 编译时验证需求

#### 需求 REQ-005: 类型一致性验证
**优先级**: P0 (必须实现)
**复杂度**: 高
**依赖关系**: 依赖 REQ-001~004

**功能描述**:
在编译时验证默认值类型与字段类型的严格一致性，提供友好的错误消息

**用户故事**:
作为开发者，我希望类型不匹配的错误能在编译时被发现，并得到清晰的错误消息和修复建议。

**详细功能要求**:
1. **验证规则**：
   ```rust
   // 正确的类型匹配
   #[attr(default = "hello")]
   name: String,  // ✓ 字符串 → String
   
   #[attr(default = "42")]
   age: i32,      // ✓ 数字字符串 → i32
   
   // 错误的类型匹配
   #[attr(default = "not_a_number")]
   age: i32,      // ✗ 编译错误
   
   #[attr(default = "123")]
   config: serde_json::Value,  // ✗ 编译错误，应使用 JSON 格式
   ```

2. **错误消息格式**：
   ```
   error: 默认值类型不匹配
     --> src/lib.rs:15:25
      |
   15 |     #[attr(default = "abc")]
      |                     ^^^^^ 字符串不能作为 i32 字段的默认值
      |
      = help: 请使用数字字面量，如 `default = "42"`
      = note: 支持的数字格式：整数、负数、十六进制 (0x)、八进制 (0o)、二进制 (0b)
   ```

3. **修复建议**：
   - 每种错误提供具体的修复示例
   - 列出该类型支持的默认值格式
   - 指向相关文档链接

**验收标准**:
- [ ] 所有类型不匹配都能在编译时捕获
- [ ] 错误消息包含精确的位置信息
- [ ] 每个错误都有具体的修复建议
- [ ] 错误消息易于理解，包含示例

**技术实现点**:
- 扩展 `MacroError` 枚举添加默认值相关错误类型
- 实现 `ValidationPipeline` 进行分层验证
- 使用 `syn::Error` 的 span 功能提供精确位置
- 错误消息模板化和国际化支持

#### 需求 REQ-006: 综合验证流水线
**优先级**: P0 (必须实现)
**复杂度**: 高
**依赖关系**: 依赖 REQ-005

**功能描述**:
实现分层的验证流水线，从语法到语义进行全面验证

**详细功能要求**:
1. **验证层次**：
   ```
   语法验证 → 类型验证 → 语义验证
   ↓           ↓           ↓
   属性格式     类型匹配     业务规则
   ```

2. **验证管道**：
   - **语法层**：属性格式、参数语法、引号匹配
   - **类型层**：默认值与字段类型的兼容性
   - **语义层**：JSON 约束、Option 约束、范围检查

3. **错误收集策略**：
   - 尽可能收集所有错误，而不是遇到第一个错误就停止
   - 按严重程度排序错误消息
   - 相关错误的分组显示

**验收标准**:
- [ ] 能够一次性发现多个字段的错误
- [ ] 错误按逻辑分组和优先级排序
- [ ] 验证性能满足 < 1ms/字段的要求
- [ ] 验证结果包含详细的诊断信息

### 2.3 代码生成增强需求

#### 需求 REQ-007: toNode/toMark 方法增强
**优先级**: P0 (必须实现)
**复杂度**: 中等
**依赖关系**: 依赖 REQ-001~006

**功能描述**:
增强现有的 `toNode` 和 `toMark` 方法，支持默认值的智能处理

**用户故事**:
作为开发者，我希望生成的 `toNode` 方法能够自动处理默认值，当字段值为 None 或未设置时，自动使用声明的默认值。

**详细功能要求**:
1. **默认值处理逻辑**：
   ```rust
   // 原始结构体
   #[derive(Node)]
   #[node_type = "paragraph"]
   pub struct Paragraph {
       #[attr(default = "默认内容")]
       content: String,
       
       #[attr]
       author: Option<String>,
   }
   
   // 生成的 toNode 方法
   impl Paragraph {
       pub fn to_node(&self) -> mf_core::node::Node {
           // ... 现有逻辑保持不变 ...
           
           // 使用默认值的智能处理
           node.set_attr("content", serde_json::Value::String(
               if self.content.is_empty() {
                   "默认内容".to_string()
               } else {
                   self.content.clone()
               }
           ));
           
           // 无默认值字段保持现有逻辑
           if let Some(ref author) = self.author {
               node.set_attr("author", serde_json::Value::String(author.clone()));
           }
           
           node
       }
   }
   ```

2. **智能判断逻辑**：
   - Option 类型：None 时使用默认值
   - String 类型：空字符串时使用默认值
   - 数值类型：零值时使用默认值（可配置）
   - 布尔类型：始终使用实际值

3. **向后兼容**：
   - 无默认值字段的处理逻辑完全不变
   - 方法签名保持一致
   - 现有调用代码无需修改

**验收标准**:
- [ ] 生成的方法与现有方法完全兼容
- [ ] 默认值在适当时机被正确应用
- [ ] 性能开销最小，无额外运行时检查
- [ ] 生成的代码易于阅读和调试

#### 需求 REQ-008: 构造函数生成
**优先级**: P1 (重要实现)
**复杂度**: 中等
**依赖关系**: 依赖 REQ-007

**功能描述**:
生成支持默认值的便利构造函数，简化对象创建过程

**用户故事**:
作为开发者，我希望能够使用简单的构造函数创建对象，不需要手动设置每个字段的默认值。

**详细功能要求**:
1. **new() 方法生成**：
   ```rust
   impl Paragraph {
       /// 使用所有默认值创建实例
       pub fn new() -> Self {
           Self {
               content: "默认内容".to_string(),
               author: None,
           }
       }
   }
   ```

2. **with_defaults() 方法生成**：
   ```rust
   impl Paragraph {
       /// 创建实例，允许覆盖部分字段
       pub fn with_defaults() -> ParagraphBuilder {
           ParagraphBuilder::new()
               .content("默认内容")
       }
   }
   ```

3. **生成条件**：
   - 只有当结构体有默认值字段时才生成 `new()` 方法
   - `with_defaults()` 方法为可选功能
   - 不与现有方法冲突

**验收标准**:
- [ ] `new()` 方法正确使用所有默认值
- [ ] 只在有默认值时生成构造函数
- [ ] 生成的构造函数类型安全
- [ ] 构造函数性能优良，无额外开销

### 2.4 高级功能需求

#### 需求 REQ-009: 表达式默认值支持 (未来功能)
**优先级**: P2 (可选实现)
**复杂度**: 很高
**依赖关系**: 依赖所有核心功能

**功能描述**:
支持函数调用和表达式作为默认值，提供更强大的默认值设置能力

**详细功能要求**:
```rust
#[derive(Node)]
#[node_type = "timestamp"]
pub struct TimestampNode {
    // 函数调用默认值
    #[attr(default = "Utc::now().to_rfc3339()")]
    created_at: String,
    
    // 常量引用默认值
    #[attr(default = "DEFAULT_CONFIG")]
    config: serde_json::Value,
    
    // 条件表达式默认值
    #[attr(default = "if cfg!(debug_assertions) { \"debug\" } else { \"release\" }")]
    build_mode: String,
}
```

**实现考虑**:
- 安全性：只允许特定的安全函数调用
- 编译时评估：尽可能在编译时计算表达式
- 依赖管理：表达式中的依赖项管理

#### 需求 REQ-010: IDE 支持增强 (未来功能)
**优先级**: P2 (可选实现)
**复杂度**: 中等
**依赖关系**: 依赖核心功能稳定

**功能描述**:
为 IDE 提供更好的默认值支持，包括自动补全、语法高亮、错误提示

**详细功能要求**:
- 默认值的语法高亮
- 类型不匹配的实时错误提示
- 默认值格式的自动补全
- 重构时的默认值同步更新

## 3. 非功能性需求

### 3.1 性能需求

#### 需求 NFR-001: 编译时性能
**要求等级**: 必须满足
**指标定义**:
- 单个字段的默认值解析和验证时间 < 1ms
- JSON 格式验证时间 < 2ms
- 整体编译时间增加 < 10%
- 内存使用峰值增加 < 20MB

**测试方法**:
- 基准测试：对比启用/关闭默认值功能的编译时间
- 压力测试：100+ 字段的大型结构体编译测试
- 内存分析：使用 `cargo flamegraph` 分析内存使用

#### 需求 NFR-002: 运行时性能
**要求等级**: 必须满足
**指标定义**:
- 默认值处理的运行时开销为零
- 生成的代码性能与手写代码相当
- 不引入额外的运行时依赖

### 3.2 兼容性需求

#### 需求 NFR-003: 向后兼容性
**要求等级**: 必须满足
**指标定义**:
- 现有代码 100% 无需修改即可编译通过
- 现有 API 和行为完全保持不变
- 新功能作为可选扩展，不影响现有功能

#### 需求 NFR-004: 前向兼容性
**要求等级**: 应该满足
**指标定义**:
- 预留扩展点支持未来功能添加
- API 设计考虑版本演进需求
- 配置格式支持向后兼容的升级

### 3.3 可用性需求

#### 需求 NFR-005: 错误消息质量
**要求等级**: 必须满足
**指标定义**:
- 错误消息的用户友好度评分 ≥ 90%
- 每个错误都有具体的修复建议
- 错误定位精确到字符级别
- 错误消息支持中文本地化

#### 需求 NFR-006: 学习成本
**要求等级**: 应该满足
**指标定义**:
- 新功能上手时间 < 30 分钟
- 文档覆盖率 ≥ 90%
- 提供完整的示例和最佳实践
- 从现有语法到新语法的迁移指南

### 3.4 维护性需求

#### 需求 NFR-007: 代码质量
**要求等级**: 必须满足
**指标定义**:
- 测试覆盖率 ≥ 95%
- 所有公共 API 都有详细文档
- 代码遵循项目的编码规范
- 核心设计原则的严格遵守

#### 需求 NFR-008: 扩展性
**要求等级**: 应该满足
**指标定义**:
- 支持自定义默认值验证器
- 支持自定义代码生成模板
- 插件式架构支持第三方扩展
- 清晰的扩展点和API边界

## 4. 验收标准总结

### 4.1 功能验收矩阵

| 需求编号 | 功能描述 | 验收标准 | 测试方法 |
|---------|----------|----------|----------|
| REQ-001 | 基础语法支持 | 解析 `#[attr(default="value")]` | 单元测试 + 集成测试 |
| REQ-002 | 简单类型支持 | 支持所有基本类型的默认值 | 类型覆盖测试 |
| REQ-003 | JSON 格式支持 | 解析和验证 JSON 默认值 | JSON 格式测试 |
| REQ-004 | Option 类型支持 | 处理 None 和 Some 默认值 | Option 专项测试 |
| REQ-005 | 类型验证 | 编译时捕获所有类型错误 | 编译失败测试 |
| REQ-006 | 验证流水线 | 分层验证和错误收集 | 验证逻辑测试 |
| REQ-007 | 方法增强 | 增强 toNode/toMark 方法 | 代码生成测试 |
| REQ-008 | 构造函数生成 | 生成便利构造函数 | 构造函数测试 |

### 4.2 质量验收标准

#### 功能完整性
- [ ] 所有 P0 需求 100% 实现
- [ ] 所有 P1 需求 ≥ 80% 实现
- [ ] 向后兼容性 100% 保证

#### 性能指标
- [ ] 编译时间增加 < 10%
- [ ] 内存使用增加 < 20MB
- [ ] 运行时零额外开销

#### 质量指标
- [ ] 测试覆盖率 ≥ 95%
- [ ] 文档覆盖率 ≥ 90%
- [ ] 所有错误情况有测试覆盖

#### 用户体验
- [ ] 错误消息清晰友好
- [ ] 学习成本满足目标
- [ ] 迁移路径平滑

## 5. 风险分析与缓解

### 5.1 技术风险

#### 风险 RISK-001: 编译时验证复杂性
**风险等级**: 中高
**风险描述**: 编译时类型验证逻辑复杂，可能引入性能问题或验证错误
**影响评估**: 可能导致编译时间显著增加，或产生误报/漏报
**缓解措施**:
1. 分阶段实现，先支持简单类型再支持复杂类型
2. 建立完善的基准测试和性能监控
3. 实现验证结果缓存，避免重复验证
4. 提供验证器的可插拔架构，支持性能优化

#### 风险 RISK-002: 类型系统兼容性
**风险等级**: 中等
**风险描述**: 复杂的泛型和类型别名可能导致类型识别错误
**影响评估**: 部分合法的类型组合无法被正确识别和处理
**缓解措施**:
1. 建立全面的类型测试用例库
2. 使用 `syn` 库的完整类型解析功能
3. 提供类型别名的显式支持
4. 建立类型识别的回退机制

### 5.2 集成风险

#### 风险 RISK-003: 现有代码兼容性
**风险等级**: 高
**风险描述**: 新功能可能意外破坏现有代码的编译或行为
**影响评估**: 严重影响用户升级体验，可能导致功能回退
**缓解措施**:
1. 建立全面的回归测试套件
2. 使用现有代码库进行兼容性测试
3. 实现严格的功能隔离，新功能不影响现有路径
4. 提供功能开关，支持渐进式启用

#### 风险 RISK-004: 依赖版本冲突
**风险等级**: 中等
**风险描述**: 新增或升级的依赖可能与现有生态产生冲突
**影响评估**: 可能导致用户项目的依赖解析失败
**缓解措施**:
1. 最小化新增外部依赖
2. 使用工作空间统一的依赖版本
3. 进行依赖兼容性测试
4. 提供依赖版本的向后兼容保证

### 5.3 用户体验风险

#### 风险 RISK-005: 学习曲线陡峭
**风险等级**: 中等
**风险描述**: 新语法和概念可能增加用户学习成本
**影响评估**: 可能影响功能采用率和用户满意度
**缓解措施**:
1. 提供详细的文档和示例
2. 设计渐进式的学习路径
3. 提供从现有用法到新用法的迁移工具
4. 建立社区支持和FAQ

## 6. 成功指标

### 6.1 技术指标
- **功能覆盖率**: 100% 的规划类型支持默认值
- **编译验证**: 100% 的类型错误在编译时捕获
- **性能影响**: 编译时间增加 < 10%
- **测试覆盖**: ≥ 95% 的代码路径覆盖

### 6.2 质量指标
- **兼容性**: 现有代码 100% 向后兼容
- **错误体验**: 用户友好度评分 ≥ 90%
- **文档质量**: 文档覆盖率 ≥ 90%
- **维护性**: 代码质量评分 ≥ 90%

### 6.3 用户体验指标
- **效率提升**: 样板代码减少 ≥ 60%
- **学习成本**: 新功能上手时间 < 30 分钟
- **错误修复**: 平均错误定位和修复时间 < 5 分钟
- **采用率**: 新项目中新功能使用率 ≥ 80%

---

*此详细功能需求列表为 ModuForge-RS Default 属性扩展项目提供了全面的功能定义和验收标准，确保项目开发能够满足用户需求并保持高质量标准。*