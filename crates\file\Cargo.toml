[package]
name = "moduforge-file"
version = {workspace=true}
edition = {workspace=true}
description = "moduforge 文件"
authors = {workspace=true}
license = {workspace=true}
documentation = {workspace=true}
homepage = {workspace=true}
repository = {workspace=true}
[lib]
name = "mf_file"
path = "src/lib.rs"
[dependencies]
memmap2 = "0.9"
crc32fast = "1.4"
thiserror = { workspace = true }
bincode = { version = "2.0.1", features = ["serde"] }
serde = { workspace = true, features = ["derive"] }
zstd = "0.13"
blake3 = "1"
zip = { version = "4.3.0", default-features = false, features = ["deflate"] }
serde_json = { workspace = true }
rmp-serde = "1.3"
serde_cbor = "0.11"
anyhow = { workspace = true }
rayon = { workspace = true }



[dev-dependencies]
tempfile = { workspace = true }
tokio = { workspace = true}
moduforge-state = { workspace = true }
moduforge-model = { workspace = true }
moduforge-core = { workspace = true }
criterion = { workspace = true }