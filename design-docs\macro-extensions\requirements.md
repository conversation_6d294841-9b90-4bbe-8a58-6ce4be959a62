# ModuForge-RS 宏模块扩展 - 需求规格说明

## 1. 项目概述

### 1.1 项目背景
基于 ModuForge-RS 框架现有的 `mf-derive` 和 `mf-macro` 模块，通过添加新的派生宏功能，简化开发者创建 `mf_core::node::Node` 和 `mf_core::mark::Mark` 实例的过程。

### 1.2 项目目标
- **主要目标**: 在现有模块中添加 `#[derive(Node)]` 和 `#[derive(Mark)]` 派生宏
- **次要目标**: 提供编译时验证和类型安全保障
- **约束条件**: 严格在现有 `mf-derive` 模块中扩展，不创建新的 crate

### 1.3 目标用户
- ModuForge-RS 框架使用者
- 业务应用开发者
- 插件和扩展开发者

## 2. 功能需求

### 2.1 Node 派生宏需求

#### 2.1.1 基本功能
**需求编号**: REQ-001  
**需求描述**: 为结构体提供 `#[derive(Node)]` 派生宏支持

**输入格式**:
```rust
#[derive(Node, Serialize, Deserialize)]
#[node_type = "GCXM", marks="color", content="DCXM"]
pub struct ConstructProject {
    #[attr]
    name: String,
    #[attr]
    age: Option<i32>
}
```

**输出要求**:
```rust
impl ConstructProject {
    /// 将结构体转换为 mf_core::node::Node 实例
    pub fn to_node(&self) -> mf_core::node::Node {
        let mut node = mf_core::node::Node::create("", Default::default());
        // 生成的转换逻辑
        node
    }
}
```

#### 2.1.2 属性配置支持
**需求编号**: REQ-002  
**需求描述**: 支持以下宏属性配置

| 属性名 | 必需性 | 描述 | 示例 |
|--------|--------|------|------|
| `node_type` | 必需 | 节点类型标识符 | `#[node_type = "GCXM"]` |
| `marks` | 可选 | 支持的标记类型 | `#[marks = "color,bold"]` |
| `content` | 可选 | 内容约束表达式 | `#[content = "DCXM"]` |

#### 2.1.3 字段属性支持
**需求编号**: REQ-003  
**需求描述**: 支持 `#[attr]` 字段标记

- **功能**: 标记结构体字段作为节点属性
- **类型支持**: `String`, `i32`, `i64`, `f32`, `f64`, `bool`, `Option<T>`
- **转换规则**: 自动转换为 `serde_json::Value` 类型

### 2.2 Mark 派生宏需求

#### 2.2.1 基本功能
**需求编号**: REQ-004  
**需求描述**: 为结构体提供 `#[derive(Mark)]` 派生宏支持

**输入格式**:
```rust
#[derive(Mark, Serialize, Deserialize)]
#[mark_type = "color"]
pub struct Color {
    #[attr]
    name: String,
    #[attr]
    intensity: Option<f32>
}
```

**输出要求**:
```rust
impl Color {
    /// 将结构体转换为 mf_core::mark::Mark 实例
    pub fn to_mark(&self) -> mf_core::mark::Mark {
        let mut mark = mf_core::mark::Mark::new("", Default::default());
        // 生成的转换逻辑
        mark
    }
}
```

#### 2.2.2 属性配置支持
**需求编号**: REQ-005  
**需求描述**: 支持以下宏属性配置

| 属性名 | 必需性 | 描述 | 示例 |
|--------|--------|------|------|
| `mark_type` | 必需 | 标记类型标识符 | `#[mark_type = "color"]` |

### 2.3 编译时验证需求

#### 2.3.1 属性验证
**需求编号**: REQ-006  
**需求描述**: 编译时验证宏属性的正确性

**验证规则**:
1. `node_type` 和 `mark_type` 必须是非空字符串
2. `marks` 属性格式必须为逗号分隔的标识符列表
3. `#[attr]` 标记的字段类型必须支持转换为 `serde_json::Value`

**错误处理**:
- 提供清晰的编译错误信息
- 指出错误的具体位置
- 提供修复建议

#### 2.3.2 类型兼容性验证
**需求编号**: REQ-007  
**需求描述**: 验证字段类型与框架的兼容性

**支持的类型**:
- 基本类型: `String`, `i32`, `i64`, `f32`, `f64`, `bool`
- 可选类型: `Option<T>` (其中 T 为支持的基本类型)
- 复合类型: 实现了 `serde::Serialize` 的类型

## 3. 非功能性需求

### 3.1 性能需求

#### 3.1.1 编译时性能
**需求编号**: REQ-008
- **解析性能**: 单个结构体宏解析时间 < 5ms
- **代码生成**: 生成方法代码耗时 < 10ms
- **内存使用**: 宏展开过程内存峰值 < 50MB

#### 3.1.2 运行时性能
**需求编号**: REQ-009
- **转换性能**: 生成的 `to_node()` 和 `to_mark()` 方法性能接近手写代码
- **内存分配**: 最小化不必要的内存分配
- **序列化兼容**: 保持与 serde 的序列化性能一致

### 3.2 兼容性需求

#### 3.2.1 API 兼容性
**需求编号**: REQ-010
- **mf-core 兼容**: 生成的代码完全兼容 mf-core API
- **版本兼容**: 支持当前 mf-core 版本的所有功能
- **向后兼容**: 不影响现有 `mf-derive` 模块的功能

#### 3.2.2 Rust 版本兼容性
**需求编号**: REQ-011
- **最低版本**: 支持 Rust 1.70+
- **Edition 兼容**: 支持 Rust 2021 Edition
- **特性兼容**: 与 Cargo workspace 特性完全兼容

### 3.3 可维护性需求

#### 3.3.1 代码质量
**需求编号**: REQ-012
- **测试覆盖率**: 宏功能测试覆盖率 ≥ 90%
- **文档覆盖率**: 公共 API 文档覆盖率 = 100%
- **代码规范**: 通过所有 clippy 检查，零警告策略

#### 3.3.2 错误处理
**需求编号**: REQ-013
- **错误信息**: 提供详细、友好的编译错误信息
- **错误定位**: 精确指出错误在源代码中的位置
- **修复建议**: 为常见错误提供具体的修复建议

## 4. 技术约束

### 4.1 架构约束
**约束编号**: CON-001
- **模块限制**: 只能在现有 `mf-derive` 模块中添加功能
- **依赖限制**: 不能引入新的外部依赖（除必要的宏开发工具）
- **API 限制**: 必须使用 mf-core 提供的公共 API

### 4.2 实现约束
**约束编号**: CON-002
- **宏类型**: 必须使用派生宏（derive macro）实现
- **代码生成**: 使用 quote 和 syn crate 进行代码生成和解析
- **错误处理**: 使用 syn::Error 提供编译时错误信息

### 4.3 集成约束
**约束编号**: CON-003
- **构建系统**: 完全兼容现有 Cargo workspace 配置
- **CI/CD**: 通过现有的持续集成检查
- **文档系统**: 与现有 rustdoc 文档系统集成

## 5. 验收标准

### 5.1 功能验收标准

#### 5.1.1 Node 宏验收
**标准编号**: AC-001
- [ ] 支持 `#[derive(Node)]` 语法
- [ ] 支持 `node_type`, `marks`, `content` 属性配置
- [ ] 支持 `#[attr]` 字段标记
- [ ] 生成正确的 `to_node()` 方法
- [ ] 与 mf-core Node API 完全兼容

#### 5.1.2 Mark 宏验收
**标准编号**: AC-002
- [ ] 支持 `#[derive(Mark)]` 语法
- [ ] 支持 `mark_type` 属性配置
- [ ] 支持 `#[attr]` 字段标记
- [ ] 生成正确的 `to_mark()` 方法
- [ ] 与 mf-core Mark API 完全兼容

### 5.2 质量验收标准

#### 5.2.1 测试标准
**标准编号**: AC-003
- [ ] 单元测试覆盖率 ≥ 90%
- [ ] 集成测试通过率 = 100%
- [ ] 编译失败场景测试完整
- [ ] 性能基准测试达标

#### 5.2.2 代码质量标准
**标准编号**: AC-004
- [ ] 通过所有 clippy 检查
- [ ] 代码格式符合项目规范
- [ ] 公共 API 文档完整
- [ ] 错误信息友好且准确

### 5.3 性能验收标准

#### 5.3.1 编译时性能标准
**标准编号**: AC-005
- [ ] 宏解析时间 < 5ms
- [ ] 代码生成时间 < 10ms
- [ ] 内存使用峰值 < 50MB
- [ ] 对整体编译时间影响 < 5%

#### 5.3.2 运行时性能标准
**标准编号**: AC-006
- [ ] 生成方法性能与手写代码相当
- [ ] 内存分配最小化
- [ ] 序列化性能保持一致

## 6. 风险评估

### 6.1 技术风险

#### 6.1.1 宏复杂性风险
**风险等级**: 中等
**风险描述**: 复杂的宏实现可能导致维护困难
**缓解措施**: 
- 模块化设计，职责分离
- 完善的文档和注释
- 充分的测试覆盖

#### 6.1.2 编译时间风险
**风险等级**: 低等
**风险描述**: 宏可能增加编译时间
**缓解措施**:
- 优化宏解析算法
- 缓存重复计算结果
- 性能基准监控

### 6.2 兼容性风险

#### 6.2.1 API 变更风险
**风险等级**: 中等
**风险描述**: mf-core API 变更可能影响宏实现
**缓解措施**:
- 使用稳定的 API 接口
- 建立 API 变更监控
- 版本兼容性测试

#### 6.2.2 Rust 版本风险
**风险等级**: 低等
**风险描述**: 新 Rust 版本可能影响宏编译
**缓解措施**:
- 定期测试新 Rust 版本
- 使用保守的语法特性
- 建立持续集成检查

## 7. 交付物清单

### 7.1 代码交付物
1. **mf-derive 模块扩展**: 在现有模块中添加新的派生宏实现
2. **测试套件**: 完整的单元测试和集成测试
3. **示例代码**: 使用示例和最佳实践指南
4. **性能基准**: 编译时和运行时性能基准测试

### 7.2 文档交付物
1. **API 文档**: 完整的 rustdoc 文档
2. **使用指南**: 详细的使用说明和教程
3. **迁移指南**: 从手写代码迁移到使用宏的指南
4. **故障排除**: 常见问题和解决方案

### 7.3 配置交付物
1. **Cargo.toml 更新**: 必要的依赖配置更新
2. **CI/CD 配置**: 持续集成配置更新
3. **文档配置**: 文档生成配置更新

## 8. 项目里程碑

### 8.1 阶段一: 基础框架搭建 (Week 1-2)
- [ ] 更新 mf-derive Cargo.toml 依赖配置
- [ ] 创建基础的目录结构和模块框架
- [ ] 实现基础的错误类型和工具函数
- [ ] 建立测试框架和 CI 配置

### 8.2 阶段二: Node 派生宏实现 (Week 3-4)  
- [ ] 实现 Node 属性解析器
- [ ] 实现 Node 代码生成器
- [ ] 添加编译时验证逻辑
- [ ] 编写完整的测试用例

### 8.3 阶段三: Mark 派生宏实现 (Week 5-6)
- [ ] 实现 Mark 属性解析器
- [ ] 实现 Mark 代码生成器
- [ ] 复用验证和工具逻辑
- [ ] 补充 Mark 相关测试

### 8.4 阶段四: 文档和优化 (Week 7-8)
- [ ] 编写完整的 API 文档
- [ ] 创建使用示例和教程
- [ ] 性能优化和基准测试
- [ ] 社区反馈收集和问题修复

## 9. 成功指标

### 9.1 开发效率指标
- **样板代码减少**: 相比手写代码减少 80% 的样板代码
- **开发时间缩短**: 节点定义时间从 30 分钟缩短到 5 分钟
- **错误率降低**: 编译期发现 95% 的配置错误

### 9.2 质量指标
- **测试覆盖率**: ≥ 90%
- **文档完整性**: 100% API 文档覆盖
- **用户满意度**: 新用户 30 分钟内掌握基本用法

### 9.3 性能指标
- **编译时间影响**: < 5%
- **运行时性能**: 与手写代码相当
- **内存使用**: 最小化分配开销

这份需求规格说明书为 ModuForge-RS 宏模块扩展项目提供了详细的功能要求、技术约束和验收标准，确保项目能够在现有架构基础上成功实施。