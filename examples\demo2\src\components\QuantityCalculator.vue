<template>
  <a-modal
    v-model:open="visible"
    title="工程量计算器"
    width="800px"
    :footer="null"
    :maskClosable="false"
    @cancel="handleCancel"
  >
    <div class="quantity-calculator">
      <!-- 项目信息 -->
      <div class="item-info">
        <a-descriptions :column="3" size="small" bordered>
          <a-descriptions-item label="项目编码">{{ currentItem?.code || '-' }}</a-descriptions-item>
          <a-descriptions-item label="项目名称">{{ currentItem?.name || '-' }}</a-descriptions-item>
          <a-descriptions-item label="计量单位">{{ currentItem?.unit || '-' }}</a-descriptions-item>
        </a-descriptions>
      </div>

      <!-- 计算区域 -->
      <div class="calculator-area">
        <a-row :gutter="16">
          <!-- 左侧：计算表格 -->
          <a-col :span="14">
            <div class="calculation-table">
              <div class="table-header">
                <span>计算表格</span>
                <a-space>
                  <a-button size="small" @click="addRow">
                    <template #icon><PlusOutlined /></template>
                    添加行
                  </a-button>
                  <a-button size="small" @click="deleteSelected" :disabled="selectedRowKeys.length === 0">
                    <template #icon><DeleteOutlined /></template>
                    删除
                  </a-button>
                  <a-button size="small" @click="clearAll">
                    <template #icon><ClearOutlined /></template>
                    清空
                  </a-button>
                </a-space>
              </div>

              <a-table
                :columns="calculationColumns"
                :data-source="calculationData"
                :pagination="false"
                :scroll="{ y: 200 }"
                size="small"
                bordered
                row-key="id"
                :row-selection="rowSelection"
              >
                <template #bodyCell="{ column, record, index }">
                  <template v-if="column.key === 'index'">
                    {{ index + 1 }}
                  </template>
                  <template v-else-if="column.key === 'description'">
                    <a-input
                      v-model:value="record.description"
                      placeholder="计算说明"
                      @change="calculateTotal"
                    />
                  </template>
                  <template v-else-if="column.key === 'formula'">
                    <a-input
                      v-model:value="record.formula"
                      placeholder="如：10*5*2"
                      @change="calculateRowResult(record)"
                    />
                  </template>
                  <template v-else-if="column.key === 'result'">
                    <span class="result-value">{{ formatNumber(record.result) }}</span>
                  </template>
                  <template v-else-if="column.key === 'actions'">
                    <a-button type="link" size="small" @click="deleteRow(record.id)">
                      <template #icon><DeleteOutlined /></template>
                    </a-button>
                  </template>
                </template>
              </a-table>
            </div>
          </a-col>

          <!-- 右侧：计算器和结果 -->
          <a-col :span="10">
            <div class="calculator-panel">
              <!-- 计算器 -->
              <div class="calculator">
                <div class="calculator-header">
                  <span>计算器</span>
                  <a-button size="small" @click="clearCalculator">
                    <template #icon><ClearOutlined /></template>
                  </a-button>
                </div>
                
                <div class="calculator-display">
                  <a-input
                    v-model:value="calculatorInput"
                    placeholder="输入计算公式"
                    @keyup.enter="calculateExpression"
                  />
                </div>

                <div class="calculator-buttons">
                  <a-row :gutter="[4, 4]">
                    <a-col :span="6" v-for="btn in calculatorButtons" :key="btn.value">
                      <a-button 
                        :type="btn.type || 'default'"
                        size="small"
                        block
                        @click="handleCalculatorButton(btn)"
                      >
                        {{ btn.label }}
                      </a-button>
                    </a-col>
                  </a-row>
                </div>

                <div class="calculator-result">
                  <span>结果：{{ calculatorResult }}</span>
                  <a-button 
                    type="link" 
                    size="small" 
                    @click="useCalculatorResult"
                    :disabled="!calculatorResult"
                  >
                    使用结果
                  </a-button>
                </div>
              </div>

              <!-- 汇总结果 -->
              <div class="summary-result">
                <a-card size="small" title="汇总结果">
                  <div class="summary-item">
                    <span>总工程量：</span>
                    <span class="total-value">{{ formatNumber(totalQuantity) }}</span>
                    <span class="unit">{{ currentItem?.unit || '' }}</span>
                  </div>
                  <div class="summary-item">
                    <span>计算行数：</span>
                    <span>{{ calculationData.length }}</span>
                  </div>
                  <div class="summary-item">
                    <span>最后更新：</span>
                    <span>{{ lastUpdateTime }}</span>
                  </div>
                </a-card>
              </div>

              <!-- 常用公式 -->
              <div class="common-formulas">
                <a-card size="small" title="常用公式">
                  <div class="formula-list">
                    <div 
                      v-for="formula in commonFormulas" 
                      :key="formula.name"
                      class="formula-item"
                      @click="useFormula(formula)"
                    >
                      <span class="formula-name">{{ formula.name }}</span>
                      <span class="formula-expression">{{ formula.expression }}</span>
                    </div>
                  </div>
                </a-card>
              </div>
            </div>
          </a-col>
        </a-row>
      </div>

      <!-- 底部按钮 -->
      <div class="footer-actions">
        <a-space>
          <a-button type="primary" @click="confirmCalculation">
            确定 ({{ formatNumber(totalQuantity) }} {{ currentItem?.unit || '' }})
          </a-button>
          <a-button @click="saveTemplate">保存模板</a-button>
          <a-button @click="loadTemplate">加载模板</a-button>
          <a-button @click="handleCancel">取消</a-button>
        </a-space>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  DeleteOutlined,
  ClearOutlined
} from '@ant-design/icons-vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  currentItem: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:visible', 'calculate'])

// 状态
const selectedRowKeys = ref([])
const calculatorInput = ref('')
const calculatorResult = ref('')
const lastUpdateTime = ref('')

// 计算表格列定义
const calculationColumns = [
  {
    title: '#',
    key: 'index',
    width: 40,
    align: 'center'
  },
  {
    title: '计算说明',
    key: 'description',
    width: 120
  },
  {
    title: '计算公式',
    key: 'formula',
    width: 150
  },
  {
    title: '结果',
    key: 'result',
    width: 80,
    align: 'right'
  },
  {
    title: '操作',
    key: 'actions',
    width: 60,
    align: 'center'
  }
]

// 计算数据
const calculationData = ref([
  {
    id: 1,
    description: '基础开挖',
    formula: '10*5*1.5',
    result: 75
  },
  {
    id: 2,
    description: '工作面',
    formula: '(10+0.6*2)*(5+0.6*2)*1.5',
    result: 103.68
  }
])

// 计算器按钮
const calculatorButtons = [
  { label: '7', value: '7' },
  { label: '8', value: '8' },
  { label: '9', value: '9' },
  { label: '÷', value: '/' },
  { label: '4', value: '4' },
  { label: '5', value: '5' },
  { label: '6', value: '6' },
  { label: '×', value: '*' },
  { label: '1', value: '1' },
  { label: '2', value: '2' },
  { label: '3', value: '3' },
  { label: '-', value: '-' },
  { label: '0', value: '0' },
  { label: '.', value: '.' },
  { label: '=', value: '=', type: 'primary' },
  { label: '+', value: '+' },
  { label: '(', value: '(' },
  { label: ')', value: ')' },
  { label: 'C', value: 'clear', type: 'danger' },
  { label: '←', value: 'backspace' }
]

// 常用公式
const commonFormulas = [
  {
    name: '矩形面积',
    expression: '长×宽',
    template: 'L*W'
  },
  {
    name: '矩形体积',
    expression: '长×宽×高',
    template: 'L*W*H'
  },
  {
    name: '圆形面积',
    expression: 'π×半径²',
    template: '3.14159*R*R'
  },
  {
    name: '圆形体积',
    expression: 'π×半径²×高',
    template: '3.14159*R*R*H'
  }
]

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const totalQuantity = computed(() => {
  return calculationData.value.reduce((sum, item) => sum + (item.result || 0), 0)
})

// 行选择配置
const rowSelection = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys) => {
    selectedRowKeys.value = keys
  }
}

// 方法
const formatNumber = (value) => {
  if (value === null || value === undefined) return ''
  return Number(value).toLocaleString('zh-CN', { 
    minimumFractionDigits: 0,
    maximumFractionDigits: 4 
  })
}

const calculateRowResult = (record) => {
  try {
    if (record.formula) {
      // 简单的数学表达式计算
      const result = Function(`"use strict"; return (${record.formula})`)()
      record.result = Number(result.toFixed(4))
    } else {
      record.result = 0
    }
  } catch (error) {
    record.result = 0
    message.warning('公式格式错误')
  }
  calculateTotal()
}

const calculateTotal = () => {
  lastUpdateTime.value = new Date().toLocaleTimeString()
}

const addRow = () => {
  const newRow = {
    id: Date.now(),
    description: '',
    formula: '',
    result: 0
  }
  calculationData.value.push(newRow)
}

const deleteRow = (id) => {
  calculationData.value = calculationData.value.filter(item => item.id !== id)
  calculateTotal()
}

const deleteSelected = () => {
  if (selectedRowKeys.value.length === 0) return
  
  calculationData.value = calculationData.value.filter(
    item => !selectedRowKeys.value.includes(item.id)
  )
  selectedRowKeys.value = []
  calculateTotal()
  message.success('删除成功')
}

const clearAll = () => {
  calculationData.value = []
  selectedRowKeys.value = []
  calculateTotal()
  message.success('已清空')
}

const handleCalculatorButton = (btn) => {
  switch (btn.value) {
    case 'clear':
      calculatorInput.value = ''
      calculatorResult.value = ''
      break
    case 'backspace':
      calculatorInput.value = calculatorInput.value.slice(0, -1)
      break
    case '=':
      calculateExpression()
      break
    default:
      calculatorInput.value += btn.value
      break
  }
}

const calculateExpression = () => {
  try {
    if (calculatorInput.value) {
      const result = Function(`"use strict"; return (${calculatorInput.value})`)()
      calculatorResult.value = Number(result.toFixed(4))
    }
  } catch (error) {
    message.warning('计算表达式错误')
    calculatorResult.value = ''
  }
}

const clearCalculator = () => {
  calculatorInput.value = ''
  calculatorResult.value = ''
}

const useCalculatorResult = () => {
  if (calculatorResult.value) {
    addRow()
    const lastRow = calculationData.value[calculationData.value.length - 1]
    lastRow.formula = calculatorInput.value
    lastRow.result = calculatorResult.value
    calculateTotal()
    clearCalculator()
  }
}

const useFormula = (formula) => {
  calculatorInput.value = formula.template
  message.info(`已使用公式：${formula.name}`)
}

const confirmCalculation = () => {
  const result = {
    quantity: totalQuantity.value,
    formula: calculationData.value.map(item => 
      `${item.description}: ${item.formula} = ${item.result}`
    ).join('\n'),
    details: calculationData.value
  }
  
  emit('calculate', result)
  handleCancel()
  message.success('工程量计算完成')
}

const saveTemplate = () => {
  message.info('保存模板功能开发中...')
  // TODO: 实现模板保存功能
}

const loadTemplate = () => {
  message.info('加载模板功能开发中...')
  // TODO: 实现模板加载功能
}

const handleCancel = () => {
  visible.value = false
  // 重置状态
  selectedRowKeys.value = []
  calculatorInput.value = ''
  calculatorResult.value = ''
}

// 监听visible变化
watch(() => props.visible, (newVisible) => {
  if (newVisible && props.currentItem) {
    // 根据当前项目初始化计算数据
    lastUpdateTime.value = new Date().toLocaleTimeString()
  }
})

// 监听计算数据变化
watch(calculationData, () => {
  calculateTotal()
}, { deep: true })
</script>

<style scoped>
.quantity-calculator {
  display: flex;
  flex-direction: column;
  height: 600px;
}

.item-info {
  margin-bottom: 16px;
}

.calculator-area {
  flex: 1;
  min-height: 0;
}

.calculation-table {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.table-header {
  padding: 8px 12px;
  background: #fafafa;
  border: 1px solid #e8e8e8;
  border-bottom: none;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 500;
}

.calculator-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.calculator {
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  padding: 12px;
}

.calculator-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-weight: 500;
}

.calculator-display {
  margin-bottom: 8px;
}

.calculator-buttons {
  margin-bottom: 8px;
}

.calculator-result {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.summary-result {
  flex-shrink: 0;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
}

.total-value {
  font-weight: 500;
  color: #1890ff;
}

.unit {
  color: #666;
  margin-left: 4px;
}

.common-formulas {
  flex: 1;
  min-height: 0;
}

.formula-list {
  max-height: 120px;
  overflow-y: auto;
}

.formula-item {
  padding: 6px 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-bottom: 4px;
}

.formula-item:hover {
  background-color: #f5f5f5;
}

.formula-name {
  display: block;
  font-weight: 500;
  font-size: 12px;
}

.formula-expression {
  display: block;
  color: #666;
  font-size: 11px;
}

.result-value {
  font-weight: 500;
  color: #262626;
}

.footer-actions {
  padding-top: 16px;
  border-top: 1px solid #e8e8e8;
  text-align: right;
}
</style>
