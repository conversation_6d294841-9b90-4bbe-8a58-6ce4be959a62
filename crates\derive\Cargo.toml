[package]
name = "moduforge-macros-derive"
version = {workspace=true}
edition = {workspace=true}
description = "ModuForge-RS 宏扩展模块，提供 Node 和 Mark 的派生宏"
authors = {workspace=true}
license = {workspace=true}
documentation = {workspace=true}
homepage = {workspace=true}
repository = {workspace=true}

[lib]
name = "mf_derive"
proc-macro = true

[dependencies]
syn = { version = "2.0", features = ["full"] }
quote = "1.0"
proc-macro2 = "1.0"

# 宏扩展新增依赖
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
once_cell = "1.19"
thiserror = "1.0"

# DI宏所需依赖
anyhow = {workspace=true}
moduforge-model = { workspace = true }
moduforge-state = { workspace = true }
moduforge-transform = { workspace = true }
moduforge-core = { workspace = true }
imbl= { workspace = true }
[dev-dependencies]
# 集成测试所需依赖
uuid = { version = "1.0", features = ["v4", "serde"] }
serde_json = "1.0"
tokio = { version = "1.0", features = ["full"] }