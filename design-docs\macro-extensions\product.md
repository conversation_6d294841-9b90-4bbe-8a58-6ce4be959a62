# ModuForge-RS 宏扩展产品愿景定义

## 产品愿景

基于 ModuForge-RS 框架的现有 moduforge-macros-derive 库，扩展 Node 和 Mark 派生宏以支持属性默认值功能。通过在编译时验证默认值类型一致性，提供更安全、更便捷的节点和标记定义体验，减少样板代码并提升开发效率。

## 核心目标

### 主要目标

1. **属性默认值支持**
   - 为 Node 和 Mark 宏的 `#[attr]` 属性添加 `default` 参数支持
   - 支持简单类型和 JSON 格式的默认值定义
   - 实现编译时类型一致性验证

2. **编译时验证**
   - 在编译期验证 default 值类型与字段类型的一致性
   - 对 JSON 格式默认值，强制要求字段类型为 `serde_json::Value`
   - 提供友好的编译错误消息和修复建议

3. **简化使用体验**
   - 消除创建结构体实例的需要，支持直接从简单值创建节点
   - 保持与现有 API 的完全向后兼容
   - 减少样板代码，提升开发效率

### 次要目标

1. **性能优化**
   - 在编译时完成所有验证，运行时零开销
   - 优化生成代码的性能和内存使用

2. **可扩展性**
   - 为未来支持更多默认值格式预留扩展点
   - 支持自定义类型转换器的注册

## 用户画像

### 主要用户群体

1. **ModuForge-RS 应用开发者**
   - 使用 ModuForge-RS 框架构建文档编辑、数据变换等应用
   - 需要频繁定义 Node 和 Mark 类型
   - 希望减少样板代码，提升开发效率

2. **框架扩展开发者**
   - 基于 ModuForge-RS 开发插件和扩展
   - 需要创建自定义的节点和标记类型
   - 对类型安全和编译时验证有较高要求

3. **库维护者**
   - ModuForge-RS 核心团队成员
   - 负责维护和扩展宏系统
   - 需要确保新功能与现有架构的兼容性

### 使用场景

1. **快速原型开发**
   - 在开发初期快速定义节点结构
   - 使用默认值减少初始化代码
   - 快速迭代和调试

2. **配置驱动开发**
   - 通过 JSON 配置文件定义节点属性默认值
   - 支持动态配置而无需重新编译
   - 适用于内容管理系统等场景

3. **类型安全开发**
   - 在编译时捕获类型不匹配错误
   - 确保默认值与字段类型一致
   - 减少运行时错误

## 功能概述

### 核心功能模块

1. **默认值属性解析器**
   - 解析 `#[attr(default="value")]` 语法
   - 支持字符串、数字、布尔值等简单类型
   - 支持 JSON 格式的复杂默认值

2. **编译时类型验证器**
   - 验证默认值与字段类型的兼容性
   - JSON 默认值与 `serde_json::Value` 类型的强制验证
   - 生成详细的编译错误消息

3. **增强代码生成器**
   - 扩展现有的 NodeGenerator 和 MarkGenerator
   - 生成包含默认值处理的 `to_node()` 和 `to_mark()` 方法
   - 保持向后兼容性

4. **类型转换系统**
   - 从字符串字面量转换为目标类型
   - JSON 字符串解析为 `serde_json::Value`
   - 错误处理和类型安全保证

### 业务流程概述

1. **宏属性解析流程**
   ```
   源码解析 → 属性提取 → 默认值解析 → 类型推断 → 验证
   ```

2. **类型验证流程**
   ```
   字段类型分析 → 默认值类型推断 → 兼容性检查 → 错误报告
   ```

3. **代码生成流程**
   ```
   配置验证 → 模板选择 → 代码生成 → 向后兼容检查
   ```


## 成功指标

### 可量化指标

1. **编译时验证覆盖率**
   - 目标：100% 的类型不匹配错误在编译时被捕获
   - 测试：使用错误类型的默认值应导致编译失败

2. **向后兼容性**
   - 目标：100% 的现有代码无需修改即可正常工作
   - 测试：现有的集成测试应全部通过

3. **代码生成性能**
   - 目标：编译时间增加不超过 10%
   - 测试：大型项目的编译时间基准测试

4. **错误消息质量**
   - 目标：90% 的开发者能够根据错误消息快速定位和修复问题
   - 测试：用户体验测试和错误消息清晰度评估

### 质量指标

1. **类型安全**
   - 所有默认值在编译时验证类型正确性
   - 不允许运行时类型转换失败

2. **可维护性**
   - 新增功能模块化，便于后续扩展
   - 代码复杂度控制在可接受范围内

3. **文档完整性**
   - 提供完整的 API 文档和使用示例
   - 包含迁移指南和最佳实践

## 风险评估

### 技术风险

1. **编译时验证复杂性**
   - 风险：复杂的类型推断可能导致编译器性能问题
   - 缓解：分阶段实现，优先支持简单类型

2. **向后兼容性挑战**
   - 风险：新功能可能破坏现有代码
   - 缓解：严格的兼容性测试和渐进式发布

3. **JSON 类型验证限制**
   - 风险：JSON 的动态特性可能限制编译时验证能力
   - 缓解：清晰的约束规则和详细的错误消息

### 业务风险

1. **用户接受度**
   - 风险：用户可能不愿意采用新的语法
   - 缓解：保持可选性，提供清晰的迁移路径

2. **维护成本**
   - 风险：增加的功能复杂性可能提高维护成本
   - 缓解：良好的架构设计和全面的测试覆盖

## 项目约束

### 技术约束

1. **Rust 版本兼容性**
   - 必须支持 Rust 2024 edition
   - 保持与现有 ModuForge-RS 框架的兼容性

2. **依赖限制**
   - 不能引入会显著增加编译时间的重型依赖
   - 必须保持与现有依赖版本的兼容性

3. **性能约束**
   - 编译时间增加不超过 10%
   - 生成代码的运行时性能不能降低

### 功能约束

1. **向后兼容性**
   - 现有的 `#[derive(Node)]` 和 `#[derive(Mark)]` 语法必须继续工作
   - 不能修改现有的公共 API

2. **类型支持范围**
   - 初期只支持基本类型和 `serde_json::Value`
   - 复杂类型支持作为后续版本功能

## 成功定义

此扩展项目被认为成功，当：

1. **功能完整性**
   - 支持所有规划的默认值类型
   - 编译时验证正确工作
   - 生成的代码质量达标

2. **质量保证**
   - 所有单元测试和集成测试通过
   - 代码覆盖率达到 95% 以上
   - 性能基准测试满足要求

3. **用户体验**
   - 提供清晰的文档和示例
   - 编译错误消息友好且有用
   - 向后兼容性完全保持

4. **生态系统集成**
   - 与现有 ModuForge-RS 功能无缝集成
   - 为未来扩展奠定良好基础
   - 社区反馈积极正面