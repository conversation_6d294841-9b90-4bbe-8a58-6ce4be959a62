{"name": "frontend", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "tauri": "tauri", "tauri:dev": "tauri dev", "tauri:build": "tauri build"}, "dependencies": {"@tauri-apps/api": "^2.5.0", "@tauri-apps/plugin-dialog": "^2.2.2", "@tauri-apps/plugin-global-shortcut": "^2.2.1", "axios": "^1.10.0", "element-plus": "^2.10.2", "pinia": "^3.0.3", "tabulator-tables": "^6.3.1", "vue": "^3.5.13", "vue-router": "^4.5.1"}, "devDependencies": {"@tauri-apps/cli": "^2.5.0", "@tsconfig/node22": "^22.0.2", "@types/node": "^22.14.0", "@types/vue-router": "^2.0.0", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "npm-run-all2": "^7.0.2", "typescript": "~5.8.0", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.7.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^2.2.8"}}