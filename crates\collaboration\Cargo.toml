[package]
name = "moduforge-collaboration"
version = {workspace=true}
edition = {workspace=true}
description = "moduforge 协作系统"
authors = {workspace=true}
license = {workspace=true}
documentation = {workspace=true}
homepage = {workspace=true}
repository = {workspace=true}

[lib]
name = "mf_collab"
path = "src/lib.rs"

[dependencies]
tokio =  { workspace=true }
tracing-subscriber = { workspace=true }
futures-util = { workspace=true }
serde = { workspace=true }
serde_json = { workspace=true }
anyhow = { workspace=true }
thiserror = { workspace=true }
tracing = "0.1"
dashmap = { workspace=true }

warp = "0.3.7"
yrs-warp = { workspace=true }
yrs = { workspace=true }

[dev-dependencies]
criterion = { version = "0.5", features = ["html_reports"] }
tempfile = "3.0"
tokio-test = "0.4"

[[bench]]
name = "collaboration"
harness = false
