<template>
  <a-modal
    v-model:open="visible"
    title="新建项目"
    width="700px"
    :footer="null"
    :maskClosable="false"
    @cancel="handleCancel"
  >
    <a-steps :current="currentStep" class="wizard-steps">
      <a-step title="项目基础信息" />
      <a-step title="标准配置选择" />
      <a-step title="确认创建" />
    </a-steps>

    <div class="wizard-content">
      <!-- 第一步：项目基础信息 -->
      <div v-if="currentStep === 0" class="step-content">
        <h3>步骤 1/3：项目基础信息</h3>
        <a-form
          ref="basicFormRef"
          :model="basicForm"
          :rules="basicRules"
          layout="vertical"
        >
          <a-form-item label="项目名称" name="projectName" required>
            <a-input 
              v-model:value="basicForm.projectName"
              placeholder="请输入项目名称"
              :maxlength="100"
              show-count
            />
            <div class="form-hint">最多100个字符</div>
          </a-form-item>

          <a-form-item label="项目编码" name="projectCode">
            <a-input 
              v-model:value="basicForm.projectCode"
              placeholder="请输入项目编码或点击自动生成"
            />
            <a-checkbox v-model:checked="basicForm.autoGenerateCode" style="margin-top: 8px;">
              自动生成编码
            </a-checkbox>
            <a-button 
              v-if="!basicForm.autoGenerateCode" 
              type="link" 
              @click="generateProjectCode"
            >
              自动生成
            </a-button>
          </a-form-item>

          <a-form-item label="建设单位" name="constructionUnit" required>
            <a-auto-complete
              v-model:value="basicForm.constructionUnit"
              :options="constructionUnitOptions"
              placeholder="请输入建设单位"
              @search="onConstructionUnitSearch"
            />
          </a-form-item>

          <a-form-item label="项目描述" name="projectDescription">
            <a-textarea
              v-model:value="basicForm.projectDescription"
              placeholder="请输入项目描述（选填）"
              :rows="3"
              :maxlength="500"
              show-count
            />
          </a-form-item>
        </a-form>
      </div>

      <!-- 第二步：标准配置选择 -->
      <div v-if="currentStep === 1" class="step-content">
        <h3>步骤 2/3：标准配置选择</h3>
        <a-form
          ref="configFormRef"
          :model="configForm"
          :rules="configRules"
          layout="vertical"
        >
          <a-form-item label="文件类型" name="fileType" required>
            <a-radio-group v-model:value="configForm.fileType">
              <a-radio value="招标文件">招标文件</a-radio>
              <a-radio value="投标文件">投标文件</a-radio>
              <a-radio value="单位工程">单位工程</a-radio>
              <a-radio value="工料机法">工料机法</a-radio>
            </a-radio-group>
          </a-form-item>

          <a-form-item label="清单标准" name="listStandard" required>
            <a-select v-model:value="configForm.listStandard" @change="onListStandardChange">
              <a-select-option value="GB50500-2013">GB50500-2013</a-select-option>
              <a-select-option value="GB50500-2024">GB50500-2024</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="定额标准" name="quotaStandard" required>
            <a-select v-model:value="configForm.quotaStandard">
              <a-select-option 
                v-for="quota in availableQuotas" 
                :key="quota.value" 
                :value="quota.value"
              >
                {{ quota.label }}
              </a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item 
            v-if="configForm.quotaStandard === '河北22定额'" 
            label="估价表" 
            name="priceTable"
          >
            <a-select v-model:value="configForm.priceTable">
              <a-select-option value="省站规则">省站规则</a-select-option>
              <a-select-option value="市站规则">市站规则</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="计价模式" name="pricingMode" required>
            <a-radio-group v-model:value="configForm.pricingMode">
              <a-radio value="全费用单价">全费用单价</a-radio>
              <a-radio value="清单计价">清单计价</a-radio>
            </a-radio-group>
          </a-form-item>

          <!-- 兼容性检查结果 -->
          <a-card title="兼容性检查结果" size="small" class="compatibility-check">
            <div class="check-result">
              <div class="check-item success">
                <CheckCircleOutlined style="color: #52c41a;" />
                <span>标准组合兼容</span>
              </div>
              <div class="check-item warning">
                <ExclamationCircleOutlined style="color: #faad14;" />
                <span>建议使用22定额</span>
              </div>
            </div>
          </a-card>
        </a-form>
      </div>

      <!-- 第三步：确认创建 -->
      <div v-if="currentStep === 2" class="step-content">
        <h3>步骤 3/3：确认创建</h3>
        <div class="confirm-info">
          <a-descriptions :column="1" bordered>
            <a-descriptions-item label="项目信息">{{ basicForm.projectName }}</a-descriptions-item>
            <a-descriptions-item label="项目编码">{{ getProjectCode() }}</a-descriptions-item>
            <a-descriptions-item label="地区">{{ region.province }}{{ region.city }}</a-descriptions-item>
            <a-descriptions-item label="标准">{{ getStandardText() }}</a-descriptions-item>
            <a-descriptions-item label="模式">{{ configForm.pricingMode }}模式</a-descriptions-item>
          </a-descriptions>

          <!-- 将要创建的文件结构 -->
          <a-card title="将要创建的文件结构" size="small" class="file-structure" style="margin-top: 16px;">
            <div class="file-tree">
              <div class="tree-item folder">
                <FolderOutlined style="color: #faad14;" />
                <span>{{ getProjectCode() }}{{ basicForm.projectName }}/</span>
              </div>
              <div class="tree-item file indent-1">
                <FileOutlined style="color: #1890ff;" />
                <span>project.meta</span>
              </div>
              <div class="tree-item file indent-1">
                <FileOutlined style="color: #1890ff;" />
                <span>standards.config</span>
              </div>
              <div class="tree-item folder indent-1">
                <FolderOutlined style="color: #faad14;" />
                <span>data/</span>
              </div>
              <div class="tree-item folder indent-1">
                <FolderOutlined style="color: #faad14;" />
                <span>reports/</span>
              </div>
            </div>
          </a-card>

          <!-- 创建进度 -->
          <div v-if="creating" class="create-progress" style="margin-top: 16px;">
            <a-progress :percent="createProgress" :status="createProgress === 100 ? 'success' : 'active'" />
            <div style="margin-top: 8px; text-align: center;">
              {{ createProgress === 100 ? '创建完成' : '正在创建项目...' }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="wizard-footer">
      <a-space>
        <a-button v-if="currentStep > 0" @click="prevStep">上一步</a-button>
        <a-button v-if="currentStep < 2" type="primary" @click="nextStep">下一步</a-button>
        <a-button 
          v-if="currentStep === 2" 
          type="primary" 
          :loading="creating"
          @click="createProject"
        >
          {{ creating ? '创建中...' : '创建' }}
        </a-button>
        <a-button @click="handleCancel">取消</a-button>
      </a-space>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import {
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  FolderOutlined,
  FileOutlined
} from '@ant-design/icons-vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  businessType: {
    type: String,
    default: ''
  },
  region: {
    type: Object,
    default: () => ({ province: '', city: '' })
  }
})

const emit = defineEmits(['update:visible', 'success'])

// 当前步骤
const currentStep = ref(0)

// 创建状态
const creating = ref(false)
const createProgress = ref(0)

// 表单引用
const basicFormRef = ref()
const configFormRef = ref()

// 第一步：基础信息表单
const basicForm = ref({
  projectName: '',
  projectCode: '',
  autoGenerateCode: true,
  constructionUnit: '',
  projectDescription: ''
})

const basicRules = {
  projectName: [
    { required: true, message: '请输入项目名称', trigger: 'blur' },
    { max: 100, message: '项目名称不能超过100个字符', trigger: 'blur' }
  ],
  constructionUnit: [
    { required: true, message: '请输入建设单位', trigger: 'blur' }
  ]
}

// 建设单位自动完成选项
const constructionUnitOptions = ref([
  { value: '河北省住房和城乡建设厅' },
  { value: '石家庄市政府' },
  { value: '河北建设集团' },
  { value: '中建八局' },
  { value: '河北省交通厅' }
])

// 第二步：配置表单
const configForm = ref({
  fileType: '招标文件',
  listStandard: 'GB50500-2013',
  quotaStandard: '河北22定额',
  priceTable: '省站规则',
  pricingMode: '全费用单价'
})

const configRules = {
  fileType: [{ required: true, message: '请选择文件类型', trigger: 'change' }],
  listStandard: [{ required: true, message: '请选择清单标准', trigger: 'change' }],
  quotaStandard: [{ required: true, message: '请选择定额标准', trigger: 'change' }],
  pricingMode: [{ required: true, message: '请选择计价模式', trigger: 'change' }]
}

// 可用定额选项（根据清单标准变化）
const availableQuotas = computed(() => {
  if (configForm.value.listStandard === 'GB50500-2013') {
    return [
      { value: '河北22定额', label: '河北22定额' },
      { value: '河北12定额', label: '河北12定额' }
    ]
  } else {
    return [
      { value: '河北24定额', label: '河北24定额' },
      { value: '河北22定额', label: '河北22定额' }
    ]
  }
})

// 监听visible变化
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 方法
const generateProjectCode = () => {
  const cityCode = getCityCode(props.region.city)
  const year = new Date().getFullYear()
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
  basicForm.value.projectCode = `${cityCode}${year}${random}`
  message.success('项目编码已自动生成')
}

const getCityCode = (city) => {
  const cityCodeMap = {
    '石家庄市': 'SJZ',
    '唐山市': 'TS',
    '秦皇岛市': 'QHD',
    '邯郸市': 'HD',
    '邢台市': 'XT',
    '保定市': 'BD',
    '张家口市': 'ZJK',
    '承德市': 'CD',
    '沧州市': 'CZ',
    '廊坊市': 'LF',
    '衡水市': 'HS'
  }
  return cityCodeMap[city] || 'HB'
}

const getProjectCode = () => {
  if (basicForm.value.autoGenerateCode && !basicForm.value.projectCode) {
    generateProjectCode()
  }
  return basicForm.value.projectCode
}

const getStandardText = () => {
  return `${configForm.value.listStandard}+${configForm.value.quotaStandard}`
}

const onConstructionUnitSearch = (searchText) => {
  // 这里可以实现搜索逻辑
}

const onListStandardChange = (value) => {
  // 清单标准变化时，重置定额标准
  configForm.value.quotaStandard = availableQuotas.value[0]?.value || ''
}

const nextStep = async () => {
  if (currentStep.value === 0) {
    try {
      await basicFormRef.value.validate()
      currentStep.value++
    } catch (error) {
      message.error('请完善基础信息')
    }
  } else if (currentStep.value === 1) {
    try {
      await configFormRef.value.validate()
      currentStep.value++
    } catch (error) {
      message.error('请完善配置信息')
    }
  }
}

const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

const createProject = async () => {
  creating.value = true
  createProgress.value = 0
  
  // 模拟创建过程
  const steps = [
    { progress: 20, message: '创建项目目录...' },
    { progress: 40, message: '生成配置文件...' },
    { progress: 60, message: '初始化数据结构...' },
    { progress: 80, message: '应用标准配置...' },
    { progress: 100, message: '项目创建完成' }
  ]
  
  for (const step of steps) {
    await new Promise(resolve => setTimeout(resolve, 500))
    createProgress.value = step.progress
    message.info(step.message)
  }
  
  creating.value = false
  message.success('项目创建成功！')
  
  // 发送成功事件
  emit('success', {
    ...basicForm.value,
    ...configForm.value,
    region: props.region,
    businessType: props.businessType
  })
  
  handleCancel()
}

const handleCancel = () => {
  visible.value = false
  currentStep.value = 0
  creating.value = false
  createProgress.value = 0
  
  // 重置表单
  basicForm.value = {
    projectName: '',
    projectCode: '',
    autoGenerateCode: true,
    constructionUnit: '',
    projectDescription: ''
  }
  
  configForm.value = {
    fileType: '招标文件',
    listStandard: 'GB50500-2013',
    quotaStandard: '河北22定额',
    priceTable: '省站规则',
    pricingMode: '全费用单价'
  }
}

// 监听业务类型变化，自动生成项目编码
watch(() => props.businessType, () => {
  if (basicForm.value.autoGenerateCode) {
    generateProjectCode()
  }
})
</script>

<style scoped>
.wizard-steps {
  margin-bottom: 24px;
}

.wizard-content {
  min-height: 400px;
  padding: 24px 0;
}

.step-content h3 {
  margin-bottom: 24px;
  color: #262626;
  font-size: 16px;
  font-weight: 600;
}

.form-hint {
  font-size: 12px;
  color: #8c8c8c;
  margin-top: 4px;
}

.compatibility-check {
  margin-top: 16px;
}

.check-result {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.check-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.check-item.success {
  color: #52c41a;
}

.check-item.warning {
  color: #faad14;
}

.confirm-info {
  padding: 16px 0;
}

.file-structure {
  margin-top: 16px;
}

.file-tree {
  font-family: 'Courier New', monospace;
  font-size: 14px;
}

.tree-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 2px 0;
  line-height: 1.5;
}

.tree-item.folder {
  font-weight: 500;
}

.tree-item.file {
  color: #666;
}

.indent-1 {
  margin-left: 20px;
}

.create-progress {
  text-align: center;
}

.wizard-footer {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
  text-align: right;
}
</style>
