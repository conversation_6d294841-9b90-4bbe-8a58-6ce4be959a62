[workspace]
resolver = "2"
members = [
   "crates/*",
   "examples/demo2/src-tauri",
   "devtool-rules",
   "tools/*"
   ]
exclude = [
   "examples/*"
]

[workspace.package]
version = "0.5.1"
edition = "2024"
authors = ["String <<EMAIL>>"]
license = "MIT"

documentation = "https://github.com/Cassielxd/moduforge-rs"
homepage = "https://github.com/Cassielxd/moduforge-rs"
repository = "https://github.com/Cassielxd/moduforge-rs"

[workspace.dependencies]
tokio = { version = "1", features = ["full"] }
tokio-util = "0.7.14"
reqwest = "0.12.11"
tracing = "0.1"
futures-util = "0.3"
yrs-warp = "0.8.0"
yrs = "0.18.2"
tokio-tungstenite = "0.21.0"
url = "2.5"
ctor = "0.4.2"


serde = { version = "1.0", features = ["derive", "rc"] }
serde_json = "1.0"
tempfile="3"
anyhow="1"
# imbl 不可变数据实现
imbl = { version = "6.0.0", features = ["serde"] }
thiserror = "2.0.12"
dashmap = "6.1.0"
# 异步 trait
async-trait = "0.1"
chrono = "0.4"
async-channel = "2.3.1"
# 日志系统
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
tracing-appender = "0.2"

uuid={ version = "1.0", features = ["v4"] }
futures="0.3"
# 并行
rayon = "1.8"
# 缓存
lru = "0.14.0"
# 锁
parking_lot = "0.12"

# 哈希
ahash = "0.8"
rust_decimal = "1"
rust_decimal_macros = "1"
# 路径处理
json_dotpath = "1"
# 单次初始化
once_cell = "1"
# 图
petgraph = "0.8"
# 性能测试
criterion = "0.5"
# 内存分配
bumpalo = "3"
# 随机数
fastrand = "2"
# 时间处理
humantime = "0.1.2"
# 递归 处理
recursive = "0.1"
# 正则
regex = "1"

# 正则
regex-lite = "0.1"
# 枚举
strum = "0.27"
strum_macros = "0.27"

# XML 解析依赖
quick-xml = { version = "0.36", features = ["serialize"] }

#crates 下所有的库
moduforge-core = { version = "0.5.0", path = "crates/core" }
moduforge-state = { version = "0.5.0", path = "crates/state" }
moduforge-model = { version = "0.5.0", path = "crates/model" }
moduforge-transform = { version = "0.5.0", path = "crates/transform" }
moduforge-rules-engine = { version = "0.5.0", path = "crates/engine" }
moduforge-rules-template = { version = "0.5.0", path = "crates/template" }
moduforge-rules-expression = { version = "0.5.0", path = "crates/expression" }
moduforge-file = { version = "0.5.0", path = "crates/file" }
moduforge-persistence = { version = "0.5.0", path = "crates/persistence" }
moduforge-search = { version = "0.5.0", path = "crates/search" }
moduforge-macros = { version = "0.5.0", path = "crates/macro" }
moduforge-macros-derive = { version = "0.5.0", path = "crates/derive" }
moduforge-collaboration = { version = "0.5.0", path = "crates/collaboration" }
moduforge-collaboration-client = { version = "0.5.0", path = "crates/collaboration_client" }