# 工程造价计价软件前端界面

基于 ModuForge 微前端架构开发的工程造价计价软件，实现了河北省工程造价计价软件的核心功能界面。

## 📋 功能概述

### 🏠 控制台主界面 (Dashboard)
- **地区选择**: 支持省份、地市联动选择
- **业务类型选择**: 概算、预算、结算、审核四种业务类型
- **最近项目列表**: 显示最近打开的项目文件
- **项目操作**: 新建项目、打开项目、设置等功能

### 🏗️ 项目创建向导 (ProjectCreateWizard)
- **第一步**: 项目基础信息录入（项目名称、编码、建设单位等）
- **第二步**: 标准配置选择（清单标准、定额标准、计价模式等）
- **第三步**: 创建确认和兼容性检查

### 💼 工作台主界面 (WorkbenchMain)
- **系统功能栏**: 保存、撤销、重做等基础操作
- **项目识别栏**: 显示当前项目的基本信息和标准配置
- **主菜单栏**: 文件、编制、报表、电子标、工具、帮助等功能菜单
- **三栏布局**: 结构树 + 编辑区 + 属性面板

### 📊 分部分项编辑器 (SubdivisionEditor)
- **表格编辑**: 支持单元格双击编辑、行选择、批量操作
- **工具栏**: 新增、插入、删除、复制、粘贴、上移、下移等操作
- **标准库选择**: 双击编码列可打开清单库或定额库选择器
- **工程量计算**: 点击工程量列的计算器图标可打开工程量计算器
- **人材机明细**: 下方显示选中项目的人材机构成明细

### 🧮 工程量计算器 (QuantityCalculator)
- **计算表格**: 支持多行计算，每行包含说明、公式、结果
- **内置计算器**: 提供数字键盘和基本运算功能
- **常用公式**: 预设矩形面积、体积、圆形面积等常用计算公式
- **汇总结果**: 自动汇总所有计算行的结果

### 📚 标准库选择器 (StandardLibrarySelector)
- **清单库**: 支持按分类、单位筛选清单项目
- **定额库**: 支持按分类、单位筛选定额项目，显示单价信息
- **搜索功能**: 支持关键词搜索项目编码和名称
- **详情查看**: 可查看项目的详细信息和人材机构成

### 🔧 人材机明细 (MaterialsDetail)
- **明细表格**: 显示人工、材料、机械的消耗量和单价
- **编辑功能**: 支持双击编辑消耗量和单价
- **历史价格**: 可查看材料的历史价格变化趋势
- **类型管理**: 按人工、材料、机械分类显示

### 💰 单价构成 (PriceComposition)
- **费用汇总**: 显示人工费、材料费、机械费的汇总统计
- **构成分析**: 饼图显示各类费用的占比
- **详细构成**: 表格显示具体的人材机明细和占比
- **费率分析**: 显示企业管理费、利润、规费、税金等费率

## 🗂️ 数据结构

### 项目数据 (mockProjectData.js)
```javascript
// 项目基本信息
{
  id: 1,
  name: '某住宅小区A区工程',
  code: 'SJZ2025001',
  type: '预算',
  region: { province: '河北省', city: '石家庄市' },
  standards: {
    listStandard: 'GB50500-2013',
    quotaStandard: '河北22定额',
    pricingMode: '全费用单价'
  }
}

// 工程结构树
{
  project: {
    items: [
      {
        units: [
          {
            subdivisions: [
              { type: '单位', level: 0 },
              { type: '分部', level: 1 },
              { type: '清单', level: 2 },
              { type: '定额', level: 3 }
            ]
          }
        ]
      }
    ]
  }
}
```

## 🎨 界面特色

### 响应式设计
- 支持不同屏幕尺寸的自适应布局
- 表格支持横向和纵向滚动
- 弹窗支持拖拽和缩放

### 交互体验
- 双击编辑单元格
- 右键菜单操作
- 键盘快捷键支持
- 拖拽排序功能

### 数据验证
- 实时数据验证和错误提示
- 兼容性检查和警告
- 自动计算和汇总

### 视觉效果
- 现代化的 Ant Design 组件库
- 清晰的层级结构显示
- 直观的图表和统计信息

## 🚀 技术架构

### 前端技术栈
- **Vue 3**: 组合式 API，响应式数据绑定
- **Ant Design Vue**: 企业级 UI 组件库
- **Vue Router**: 单页面应用路由管理
- **Vite**: 快速的构建工具

### 组件架构
- **主应用**: Dashboard (控制台)
- **子模块**: WorkbenchMain (工作台)
- **共享组件**: 表格、表单、弹窗等通用组件
- **业务组件**: 专业的造价计算组件

### 数据管理
- **模拟数据**: 完整的项目、标准库、人材机数据
- **状态管理**: Vue 3 响应式数据
- **数据验证**: 表单验证和业务规则检查

## 📁 文件结构

```
examples/demo2/
├── src/
│   ├── views/
│   │   ├── Dashboard.vue           # 控制台主界面
│   │   └── WorkbenchMain.vue       # 工作台主界面
│   ├── components/
│   │   ├── ProjectCreateWizard.vue # 项目创建向导
│   │   ├── SubdivisionEditor.vue   # 分部分项编辑器
│   │   ├── MaterialsDetail.vue     # 人材机明细
│   │   ├── PriceComposition.vue    # 单价构成
│   │   ├── StandardLibrarySelector.vue # 标准库选择器
│   │   └── QuantityCalculator.vue  # 工程量计算器
│   ├── data/
│   │   └── mockProjectData.js      # 模拟数据
│   └── router/
│       └── index.js                # 路由配置
```

## 🔧 开发说明

### 启动开发服务器
```bash
cd examples/demo2
npm run dev
```

### 主要功能入口
1. **控制台**: 访问 `http://localhost:5173/` 
2. **工作台**: 点击"新建预算"或"打开项目"进入工作台
3. **项目创建**: 点击"新建XX"按钮打开创建向导

### 数据模拟
- 所有数据都是模拟数据，存储在 `src/data/mockProjectData.js`
- 可以根据需要修改模拟数据来测试不同场景
- 支持地区、标准、项目类型等多维度数据

### 组件扩展
- 每个组件都是独立的，可以单独开发和测试
- 组件间通过 props 和 events 进行通信
- 支持插槽和作用域插槽的灵活扩展

## 📝 使用说明

### 创建新项目
1. 在控制台选择地区（省份、地市）
2. 选择业务类型（概算、预算、结算、审核）
3. 点击"新建XX"按钮
4. 按向导步骤填写项目信息和配置
5. 确认创建后自动进入工作台

### 编辑分部分项
1. 在工作台左侧选择单位工程
2. 切换到"分部分项"页签
3. 使用工具栏添加、删除、编辑行
4. 双击编码列选择标准库项目
5. 双击工程量列打开计算器

### 查看人材机明细
1. 在分部分项表格中选择定额行
2. 下方自动显示对应的人材机明细
3. 可以编辑消耗量和单价
4. 查看历史价格趋势

## 🎯 后续开发计划

### 功能完善
- [ ] 报表生成和打印
- [ ] 电子标书制作
- [ ] 数据导入导出
- [ ] 多项目对比分析

### 性能优化
- [ ] 大数据量表格虚拟滚动
- [ ] 组件懒加载
- [ ] 数据缓存机制

### 用户体验
- [ ] 快捷键支持
- [ ] 撤销重做功能
- [ ] 自动保存机制
- [ ] 主题切换

这个工程造价计价软件前端界面完整实现了造价软件的核心功能，提供了专业、易用的用户界面，为造价工程师提供了高效的工作工具。
