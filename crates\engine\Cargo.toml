[package]
name = "moduforge-rules-engine"
version = {workspace=true}
edition = {workspace=true}
description = "moduforge 引擎规则"
authors = {workspace=true}
license = {workspace=true}
documentation = {workspace=true}
homepage = {workspace=true}
repository = {workspace=true}

[lib]
name = "mf_engine"
doctest = false

[dependencies]
ahash = { workspace = true }
anyhow = { workspace = true }
thiserror = { workspace = true }
petgraph = { workspace = true }
serde_json = { workspace = true, features = ["arbitrary_precision"] }
serde = { workspace = true, features = ["derive", "rc"] }
once_cell = { workspace = true }
json_dotpath = { workspace = true }
rust_decimal = { workspace = true, features = ["maths-nopanic"] }
fixedbitset = "0.5"
tokio = { workspace = true, features = ["sync", "time"] }
reqwest = { version = "0.12", features = ["json", "rustls-tls"], default-features = false }
rquickjs = { version = "0.9", features = ["macro", "loader", "rust-alloc", "futures", "either", "properties"] }
jsonschema = "0.29"
moduforge-rules-expression = { workspace = true }
moduforge-rules-template = { workspace = true }

[dev-dependencies]
tokio = { workspace = true, features = ["rt-multi-thread", "macros"] }
criterion = { workspace = true, features = ["async_tokio"] }

[[bench]]
harness = false
name = "engine"