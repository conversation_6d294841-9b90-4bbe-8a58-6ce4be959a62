[package]
name = "devtool-rules-backend"
version = "0.1.0"
edition = "2021"
publish = false



[lib]
path="src/lib.rs"


[dependencies]
axum = { version = "0.7", features = ["macros"] }

tokio = { workspace=true }
tokio-util = { workspace=true,features = ["rt"] }
tower-http = { version = "0.5", features = ["fs", "trace", "compression-full", "cors"] }
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
serde = { workspace=true }
serde_json = { workspace=true }
moduforge-rules-engine = { path = "../crates/engine", version = "0.5.0" }
reqwest = { version = "0.11", features = ["blocking"] }
zip = "4.0.0"