<template>
  <div class="workbench-main">
    <!-- 系统功能栏 -->
    <div class="system-toolbar">
      <div class="toolbar-left">
        <img src="/src/assets/logo.svg" alt="Logo" class="toolbar-logo" />
        <div class="toolbar-actions">
          <a-button type="text" size="small" @click="saveProject">
            <template #icon><SaveOutlined /></template>
            保存
          </a-button>
          <a-button type="text" size="small" @click="undoAction">
            <template #icon><UndoOutlined /></template>
            撤销
          </a-button>
          <a-button type="text" size="small" @click="redoAction">
            <template #icon><RedoOutlined /></template>
            重做
          </a-button>
        </div>
      </div>
      <div class="toolbar-center">
        <span class="project-path">📁 C:\项目\某住宅小区.gcj</span>
      </div>
      <div class="toolbar-right">
        <a-dropdown>
          <a-button type="text" size="small">
            <template #icon><UserOutlined /></template>
            用户
            <DownOutlined />
          </a-button>
          <template #overlay>
            <a-menu>
              <a-menu-item key="profile">个人信息</a-menu-item>
              <a-menu-item key="settings">设置</a-menu-item>
              <a-menu-divider />
              <a-menu-item key="logout">退出登录</a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </div>
    </div>

    <!-- 项目识别栏 -->
    <div class="project-info-bar">
      <a-space>
        <a-tag color="blue">📊 预算项目</a-tag>
        <a-tag color="green">🏗️ 招标文件</a-tag>
        <a-tag color="orange">🏢 单位工程</a-tag>
        <a-tag color="purple">📋 13版清单+22版定额</a-tag>
      </a-space>
    </div>

    <!-- 主菜单栏 -->
    <div class="main-menu-bar">
      <a-menu mode="horizontal" :selected-keys="selectedMenuKeys" @click="onMenuClick">
        <a-menu-item key="file">
          <template #icon><FolderOutlined /></template>
          文件
        </a-menu-item>
        <a-menu-item key="edit">
          <template #icon><EditOutlined /></template>
          编制
        </a-menu-item>
        <a-menu-item key="report">
          <template #icon><BarChartOutlined /></template>
          报表
        </a-menu-item>
        <a-menu-item key="electronic">
          <template #icon><LaptopOutlined /></template>
          电子标
        </a-menu-item>
        <a-menu-item key="tools">
          <template #icon><ToolOutlined /></template>
          工具
        </a-menu-item>
        <a-menu-item key="help">
          <template #icon><QuestionCircleOutlined /></template>
          帮助
        </a-menu-item>
      </a-menu>
    </div>

    <!-- 主工作区 -->
    <div class="main-workspace">
      <!-- 结构树 -->
      <div class="structure-tree-panel">
        <div class="panel-header">
          <h4>工程结构树</h4>
          <a-space>
            <a-button type="text" size="small" @click="expandAll">
              <template #icon><PlusSquareOutlined /></template>
            </a-button>
            <a-button type="text" size="small" @click="collapseAll">
              <template #icon><MinusSquareOutlined /></template>
            </a-button>
          </a-space>
        </div>
        <div class="tree-content">
          <a-tree
            v-model:selectedKeys="selectedTreeKeys"
            v-model:expandedKeys="expandedTreeKeys"
            :tree-data="projectTreeData"
            :show-icon="true"
            @select="onTreeNodeSelect"
            @rightClick="onTreeRightClick"
          >
            <template #icon="{ dataRef }">
              <component :is="getTreeNodeIcon(dataRef)" />
            </template>
            <template #title="{ dataRef }">
              <span class="tree-node-title">
                {{ dataRef.title }}
                <span class="tree-node-stats">({{ dataRef.itemCount }}项, ¥{{ dataRef.amount }}万)</span>
              </span>
            </template>
          </a-tree>
        </div>
      </div>

      <!-- 编辑区 -->
      <div class="edit-area-panel">
        <div class="panel-header">
          <a-tabs v-model:activeKey="activeTabKey" @change="onTabChange">
            <a-tab-pane key="overview" tab="工程概况" />
            <a-tab-pane key="analysis" tab="造价分析" />
            <a-tab-pane key="fee-table" tab="取费表" />
            <a-tab-pane key="subdivision" tab="分部分项" v-if="showSubdivisionTab" />
            <a-tab-pane key="measures" tab="措施项目" v-if="showMeasuresTab" />
            <a-tab-pane key="materials" tab="人材机汇总" />
            <a-tab-pane key="others" tab="其他项目" v-if="showOthersTab" />
            <a-tab-pane key="summary" tab="费用汇总" v-if="showSummaryTab" />
          </a-tabs>
        </div>
        <div class="tab-content">
          <!-- 分部分项编辑 -->
          <SubdivisionEditor 
            v-if="activeTabKey === 'subdivision'"
            :project-data="currentProjectData"
            :selected-unit="selectedUnit"
            @data-change="onDataChange"
          />
          
          <!-- 其他页签内容 -->
          <div v-else class="tab-placeholder">
            <a-empty :description="`${getTabTitle(activeTabKey)}功能开发中...`" />
          </div>
        </div>
      </div>

      <!-- 属性面板 -->
      <div class="properties-panel">
        <div class="panel-header">
          <h4>属性面板</h4>
        </div>
        <div class="panel-content">
          <!-- 上下文相关信息 -->
          <a-card size="small" title="当前选中" style="margin-bottom: 16px;">
            <p><strong>节点：</strong>{{ selectedNodeInfo.title || '未选择' }}</p>
            <p><strong>类型：</strong>{{ selectedNodeInfo.type || '-' }}</p>
            <p><strong>项目数：</strong>{{ selectedNodeInfo.itemCount || 0 }}</p>
            <p><strong>金额：</strong>¥{{ selectedNodeInfo.amount || 0 }}万</p>
          </a-card>

          <!-- 数据验证结果 -->
          <a-card size="small" title="数据验证结果" style="margin-bottom: 16px;">
            <div class="validation-results">
              <div class="validation-item error" v-if="validationErrors.length > 0">
                <ExclamationCircleOutlined style="color: #ff4d4f;" />
                <span>发现 {{ validationErrors.length }} 个错误</span>
              </div>
              <div class="validation-item warning" v-if="validationWarnings.length > 0">
                <WarningOutlined style="color: #faad14;" />
                <span>发现 {{ validationWarnings.length }} 个警告</span>
              </div>
              <div class="validation-item success" v-if="validationErrors.length === 0 && validationWarnings.length === 0">
                <CheckCircleOutlined style="color: #52c41a;" />
                <span>数据验证通过</span>
              </div>
            </div>
          </a-card>

          <!-- 历史数据对比 -->
          <a-card size="small" title="历史数据对比">
            <a-select 
              v-model:value="compareProject" 
              placeholder="选择对比项目"
              style="width: 100%; margin-bottom: 12px;"
            >
              <a-select-option value="project1">某类似住宅项目</a-select-option>
              <a-select-option value="project2">某商业项目</a-select-option>
            </a-select>
            
            <div v-if="compareProject" class="compare-results">
              <div class="compare-item">
                <span>土方工程：</span>
                <span class="current-value">8.5元</span>
                <span class="vs">vs</span>
                <span class="compare-value">7.8元</span>
                <span class="change-rate up">↑ 8.9%</span>
              </div>
              <div class="compare-item">
                <span>砌体工程：</span>
                <span class="current-value">156元</span>
                <span class="vs">vs</span>
                <span class="compare-value">162元</span>
                <span class="change-rate down">↓ 3.7%</span>
              </div>
            </div>
          </a-card>
        </div>
      </div>
    </div>

    <!-- 状态栏 -->
    <div class="status-bar">
      <div class="status-left">
        <span class="status-item">就绪</span>
        <span class="status-separator">|</span>
        <span class="status-item">最后保存: {{ lastSaveTime }}</span>
      </div>
      <div class="status-right">
        <span class="status-item">共{{ totalItems }}项</span>
        <span class="status-separator">|</span>
        <span class="status-item">总造价: ¥{{ totalAmount }}万</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import SubdivisionEditor from '../components/SubdivisionEditor.vue'
import {
  SaveOutlined,
  UndoOutlined,
  RedoOutlined,
  UserOutlined,
  DownOutlined,
  FolderOutlined,
  EditOutlined,
  BarChartOutlined,
  LaptopOutlined,
  ToolOutlined,
  QuestionCircleOutlined,
  PlusSquareOutlined,
  MinusSquareOutlined,
  ExclamationCircleOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  ProjectOutlined,
  BuildOutlined,
  HomeOutlined
} from '@ant-design/icons-vue'

// 菜单状态
const selectedMenuKeys = ref(['edit'])

// 树形结构状态
const selectedTreeKeys = ref(['unit1'])
const expandedTreeKeys = ref(['project', 'item1'])

// 页签状态
const activeTabKey = ref('subdivision')

// 当前选中的单位工程
const selectedUnit = ref(null)

// 项目数据
const currentProjectData = ref({
  projectName: '某住宅小区工程',
  projectCode: 'SJZ2025001',
  totalAmount: 2850,
  totalItems: 1200
})

// 状态栏数据
const lastSaveTime = ref('14:30')
const totalItems = ref(1200)
const totalAmount = ref(2850)

// 验证结果
const validationErrors = ref([])
const validationWarnings = ref([])

// 对比项目
const compareProject = ref('')

// 项目树形数据
const projectTreeData = ref([
  {
    key: 'project',
    title: '工程项目名称',
    type: 'project',
    itemCount: 12,
    amount: 2850,
    children: [
      {
        key: 'item1',
        title: '单项1',
        type: 'item',
        itemCount: 5,
        amount: 1200,
        children: [
          {
            key: 'unit1',
            title: '单位1',
            type: 'unit',
            itemCount: 5,
            amount: 1200
          }
        ]
      },
      {
        key: 'item2',
        title: '单项2',
        type: 'item',
        itemCount: 7,
        amount: 1650,
        children: [
          {
            key: 'unit2',
            title: '单位2',
            type: 'unit',
            itemCount: 3,
            amount: 800
          },
          {
            key: 'unit3',
            title: '单位3',
            type: 'unit',
            itemCount: 4,
            amount: 850
          }
        ]
      }
    ]
  }
])

// 计算属性
const selectedNodeInfo = computed(() => {
  if (selectedTreeKeys.value.length === 0) return {}
  
  const findNode = (nodes, key) => {
    for (const node of nodes) {
      if (node.key === key) return node
      if (node.children) {
        const found = findNode(node.children, key)
        if (found) return found
      }
    }
    return null
  }
  
  return findNode(projectTreeData.value, selectedTreeKeys.value[0]) || {}
})

// 根据选中节点类型显示不同页签
const showSubdivisionTab = computed(() => selectedNodeInfo.value.type === 'unit')
const showMeasuresTab = computed(() => selectedNodeInfo.value.type === 'unit')
const showOthersTab = computed(() => selectedNodeInfo.value.type === 'unit')
const showSummaryTab = computed(() => selectedNodeInfo.value.type === 'unit')

// 方法
const getTreeNodeIcon = (node) => {
  const iconMap = {
    'project': ProjectOutlined,
    'item': BuildOutlined,
    'unit': HomeOutlined
  }
  return iconMap[node.type] || FolderOutlined
}

const getTabTitle = (key) => {
  const titleMap = {
    'overview': '工程概况',
    'analysis': '造价分析',
    'fee-table': '取费表',
    'subdivision': '分部分项',
    'measures': '措施项目',
    'materials': '人材机汇总',
    'others': '其他项目',
    'summary': '费用汇总'
  }
  return titleMap[key] || key
}

const onMenuClick = ({ key }) => {
  message.info(`点击了菜单: ${key}`)
}

const onTreeNodeSelect = (selectedKeys, { node }) => {
  if (selectedKeys.length > 0) {
    selectedUnit.value = node.dataRef
    // 如果选中单位工程，自动切换到分部分项页签
    if (node.dataRef.type === 'unit' && activeTabKey.value === 'overview') {
      activeTabKey.value = 'subdivision'
    }
  }
}

const onTreeRightClick = ({ node }) => {
  message.info(`右键点击: ${node.title}`)
  // TODO: 实现右键菜单
}

const onTabChange = (key) => {
  message.info(`切换到页签: ${getTabTitle(key)}`)
}

const onDataChange = (data) => {
  // 处理数据变更
  message.success('数据已更新')
}

const expandAll = () => {
  const getAllKeys = (nodes) => {
    let keys = []
    nodes.forEach(node => {
      keys.push(node.key)
      if (node.children) {
        keys = keys.concat(getAllKeys(node.children))
      }
    })
    return keys
  }
  expandedTreeKeys.value = getAllKeys(projectTreeData.value)
}

const collapseAll = () => {
  expandedTreeKeys.value = []
}

const saveProject = () => {
  lastSaveTime.value = new Date().toLocaleTimeString().slice(0, 5)
  message.success('项目已保存')
}

const undoAction = () => {
  message.info('撤销操作')
}

const redoAction = () => {
  message.info('重做操作')
}

onMounted(() => {
  // 初始化选中第一个单位工程
  selectedUnit.value = projectTreeData.value[0]?.children?.[0]?.children?.[0]
})
</script>

<style scoped>
.workbench-main {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f0f2f5;
}

/* 系统功能栏 */
.system-toolbar {
  height: 48px;
  background: white;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.toolbar-logo {
  height: 24px;
  width: auto;
}

.toolbar-actions {
  display: flex;
  gap: 8px;
}

.toolbar-center {
  flex: 1;
  text-align: center;
}

.project-path {
  font-size: 14px;
  color: #666;
}

.toolbar-right {
  display: flex;
  align-items: center;
}

/* 项目识别栏 */
.project-info-bar {
  height: 40px;
  background: #fafafa;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  align-items: center;
  padding: 0 16px;
}

/* 主菜单栏 */
.main-menu-bar {
  background: white;
  border-bottom: 1px solid #e8e8e8;
}

.main-menu-bar :deep(.ant-menu) {
  border-bottom: none;
}

/* 主工作区 */
.main-workspace {
  flex: 1;
  display: flex;
  min-height: 0;
}

.structure-tree-panel {
  width: 280px;
  background: white;
  border-right: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
}

.edit-area-panel {
  flex: 1;
  background: white;
  border-right: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.properties-panel {
  width: 320px;
  background: white;
  display: flex;
  flex-direction: column;
}

.panel-header {
  height: 48px;
  padding: 0 16px;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #fafafa;
}

.panel-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
}

.tree-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.tree-node-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.tree-node-stats {
  font-size: 12px;
  color: #999;
}

.tab-content {
  flex: 1;
  overflow: hidden;
}

.tab-placeholder {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.panel-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.validation-results {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.validation-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.compare-results {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.compare-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.current-value {
  font-weight: 500;
}

.vs {
  color: #999;
}

.compare-value {
  color: #666;
}

.change-rate {
  font-weight: 500;
}

.change-rate.up {
  color: #ff4d4f;
}

.change-rate.down {
  color: #52c41a;
}

/* 状态栏 */
.status-bar {
  height: 32px;
  background: #fafafa;
  border-top: 1px solid #e8e8e8;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  font-size: 12px;
  color: #666;
}

.status-left,
.status-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-separator {
  color: #d9d9d9;
}
</style>
