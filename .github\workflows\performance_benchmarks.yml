name: 性能基准测试

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]
  schedule:
    - cron: '0 2 * * *'  # 每日凌晨2点执行

env:
  RUST_BACKTRACE: 1
  CARGO_TERM_COLOR: always

jobs:
  基准测试:
    name: 运行基准测试
    runs-on: ubuntu-latest
    timeout-minutes: 60
    
    strategy:
      matrix:
        tier:
          - foundation  # 基础层: model, derive, macro
          - core-logic  # 核心逻辑层: transform, expression, template  
          - service     # 服务层: state, engine, file, search, persistence
          - integration # 集成层: core, collaboration, collaboration_client
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      with:
        fetch-depth: 0  # 获取完整历史用于回归比较
    
    - name: 设置Rust工具链
      uses: dtolnay/rust-toolchain@stable
      with:
        components: rustfmt, clippy
    
    - name: 配置缓存
      uses: actions/cache@v3
      with:
        path: |
          ~/.cargo/registry
          ~/.cargo/git
          target/
        key: benchmark-${{ runner.os }}-${{ hashFiles('**/Cargo.lock') }}-${{ matrix.tier }}
        restore-keys: |
          benchmark-${{ runner.os }}-${{ hashFiles('**/Cargo.lock') }}-
          benchmark-${{ runner.os }}-
    
    - name: 安装系统依赖
      run: |
        sudo apt-get update
        sudo apt-get install -y python3 python3-pip sqlite3
        pip3 install --user pandas matplotlib scipy numpy
    
    - name: 系统环境检查
      run: |
        echo "=== 系统信息 ==="
        echo "CPU核心数: $(nproc)"
        echo "内存信息: $(free -h)"
        echo "磁盘空间: $(df -h)"
        echo "Rust版本: $(rustc --version)"
        echo "Cargo版本: $(cargo --version)"
        echo "Python版本: $(python3 --version)"
    
    - name: 创建结果目录
      run: |
        mkdir -p benchmarks/results
        mkdir -p benchmarks/reports
    
    - name: 运行基础层基准测试
      if: matrix.tier == 'foundation'
      run: |
        echo "运行基础层基准测试..."
        cargo bench --package moduforge-model | tee benchmarks/results/model-output.txt
        cargo bench --package moduforge-macros-derive | tee benchmarks/results/derive-output.txt
        cargo bench --package moduforge-macros | tee benchmarks/results/macro-output.txt
    
    - name: 运行核心逻辑层基准测试
      if: matrix.tier == 'core-logic'
      run: |
        echo "运行核心逻辑层基准测试..."
        cargo bench --package moduforge-transform | tee benchmarks/results/transform-output.txt
        cargo bench --package moduforge-rules-expression | tee benchmarks/results/expression-output.txt
        cargo bench --package moduforge-rules-template | tee benchmarks/results/template-output.txt
    
    - name: 运行服务层基准测试
      if: matrix.tier == 'service'
      run: |
        echo "运行服务层基准测试..."
        cargo bench --package moduforge-state | tee benchmarks/results/state-output.txt
        cargo bench --package moduforge-rules-engine | tee benchmarks/results/engine-output.txt
        cargo bench --package moduforge-file | tee benchmarks/results/file-output.txt
        cargo bench --package moduforge-search | tee benchmarks/results/search-output.txt
        cargo bench --package moduforge-persistence | tee benchmarks/results/persistence-output.txt
    
    - name: 运行集成层基准测试
      if: matrix.tier == 'integration'
      run: |
        echo "运行集成层基准测试..."
        cargo bench --package moduforge-core | tee benchmarks/results/core-output.txt
        cargo bench --package moduforge-collaboration | tee benchmarks/results/collaboration-output.txt
        cargo bench --package moduforge-collaboration-client | tee benchmarks/results/collaboration-client-output.txt
    
    - name: 收集基准测试结果
      run: |
        echo "收集 ${{ matrix.tier }} 层基准测试结果..."
        
        # 创建结果汇总JSON
        cat > benchmarks/results/${{ matrix.tier }}-summary.json << 'EOF'
        {
          "tier": "${{ matrix.tier }}",
          "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
          "git_commit": "${{ github.sha }}",
          "results": []
        }
        EOF
        
        # 解析Criterion输出并生成JSON结果
        python3 scripts/parse_benchmark_output.py \
          --input-dir benchmarks/results \
          --output benchmarks/results/${{ matrix.tier }}-results.json \
          --tier ${{ matrix.tier }} \
          --commit ${{ github.sha }}
    
    - name: 上传基准测试结果
      uses: actions/upload-artifact@v3
      with:
        name: benchmark-results-${{ matrix.tier }}-${{ github.sha }}
        path: |
          benchmarks/results/${{ matrix.tier }}-results.json
          benchmarks/results/*-output.txt
        retention-days: 30
    
    - name: 性能回归检测
      if: github.event_name == 'pull_request'
      run: |
        echo "检查性能回归..."
        
        # 获取基线数据（主分支最新结果）
        BASELINE_COMMIT=$(git rev-parse origin/${{ github.base_ref }})
        echo "基线提交: $BASELINE_COMMIT"
        
        # 下载基线结果（从上次成功的CI运行）
        # 注意: 实际实现中需要从artifact storage或数据库获取
        if [ -f "benchmarks/baseline/${{ matrix.tier }}-results.json" ]; then
          echo "找到基线数据，开始回归检测..."
          python3 scripts/performance_metrics.py detect \
            benchmarks/results/${{ matrix.tier }}-results.json \
            --threshold 10.0 \
            --baseline benchmarks/baseline/${{ matrix.tier }}-results.json \
            > benchmarks/reports/${{ matrix.tier }}-regression-report.txt
          
          # 检查是否有回归
          if grep -q "🚨" benchmarks/reports/${{ matrix.tier }}-regression-report.txt; then
            echo "::warning::检测到性能回归，请查看报告"
            cat benchmarks/reports/${{ matrix.tier }}-regression-report.txt
          else
            echo "✅ 未检测到性能回归"
          fi
        else
          echo "⚠️ 未找到基线数据，跳过回归检测"
        fi
    
    - name: 生成性能报告
      run: |
        echo "生成 ${{ matrix.tier }} 性能报告..."
        
        # 创建HTML报告模板
        cat > benchmarks/reports/${{ matrix.tier }}-report.html << 'EOF'
        <!DOCTYPE html>
        <html>
        <head>
          <title>ModuForge-RS ${{ matrix.tier }} 层性能报告</title>
          <meta charset="utf-8">
          <style>
            body { font-family: Arial, sans-serif; margin: 2rem; }
            .header { background: #f5f5f5; padding: 1rem; border-radius: 5px; }
            .metric { margin: 1rem 0; padding: 1rem; border: 1px solid #ddd; }
            .regression { background-color: #ffe6e6; border-color: #ff9999; }
            .improvement { background-color: #e6ffe6; border-color: #99ff99; }
            .stable { background-color: #f0f0f0; border-color: #ccc; }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>ModuForge-RS ${{ matrix.tier }} 层性能报告</h1>
            <p>提交: ${{ github.sha }}</p>
            <p>时间: $(date -u +"%Y-%m-%d %H:%M:%S UTC")</p>
            <p>分支: ${{ github.ref_name }}</p>
          </div>
          
          <div id="content">
            <!-- 这里会被Python脚本填充实际内容 -->
          </div>
        </body>
        </html>
        EOF
        
        echo "报告模板创建完成"
    
    - name: 上传性能报告
      uses: actions/upload-artifact@v3
      with:
        name: performance-reports-${{ matrix.tier }}-${{ github.sha }}
        path: |
          benchmarks/reports/
        retention-days: 7

  汇总报告:
    name: 生成综合性能报告
    needs: [基准测试]
    runs-on: ubuntu-latest
    if: always()
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 设置Python环境
      uses: actions/setup-python@v4
      with:
        python-version: '3.x'
      
    - name: 安装Python依赖
      run: |
        pip install pandas matplotlib scipy numpy jinja2
      
    - name: 下载所有基准测试结果
      uses: actions/download-artifact@v3
      with:
        path: benchmark-artifacts/
      
    - name: 生成综合性能报告
      run: |
        echo "生成综合性能报告..."
        
        # 合并所有结果文件
        python3 -c "
        import json
        import glob
        import os
        
        all_results = []
        for file in glob.glob('benchmark-artifacts/**/*/results.json', recursive=True):
            if os.path.exists(file):
                with open(file) as f:
                    data = json.load(f)
                    if isinstance(data, list):
                        all_results.extend(data)
                    elif 'results' in data:
                        all_results.extend(data['results'])
        
        # 保存合并结果
        os.makedirs('reports', exist_ok=True)
        with open('reports/all-results.json', 'w') as f:
            json.dump(all_results, f, indent=2)
            
        print(f'合并了 {len(all_results)} 条基准测试结果')
        "
      
    - name: 生成可视化报告
      run: |
        python3 scripts/generate_comprehensive_report.py \
          --input reports/all-results.json \
          --output reports/comprehensive-report.html \
          --title "ModuForge-RS 综合性能报告" \
          --commit ${{ github.sha }}
      
    - name: 上传综合报告
      uses: actions/upload-artifact@v3
      with:
        name: comprehensive-performance-report-${{ github.sha }}
        path: reports/
        retention-days: 30
      
    - name: 部署性能报告到GitHub Pages
      if: github.ref == 'refs/heads/main' && github.event_name != 'pull_request'
      uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./reports
        destination_dir: benchmarks/${{ github.sha }}
      
    - name: 发送性能报告通知
      if: github.event_name == 'schedule'
      run: |
        if [ -n "${{ secrets.SLACK_WEBHOOK }}" ]; then
          echo "发送Slack通知..."
          
          curl -X POST -H 'Content-type: application/json' \
            --data "{
              \"text\": \"📊 ModuForge-RS 每日性能报告已生成\",
              \"attachments\": [{
                \"color\": \"good\",
                \"fields\": [{
                  \"title\": \"报告时间\",
                  \"value\": \"$(date -u +%Y-%m-%d)\",
                  \"short\": true
                }, {
                  \"title\": \"提交\",
                  \"value\": \"${{ github.sha }}\",
                  \"short\": true
                }, {
                  \"title\": \"查看报告\",
                  \"value\": \"https://${{ github.repository_owner }}.github.io/${{ github.event.repository.name }}/benchmarks/${{ github.sha }}/comprehensive-report.html\"
                }]
              }]
            }" \
            ${{ secrets.SLACK_WEBHOOK }}
        fi

  性能基线更新:
    name: 更新性能基线
    needs: [汇总报告]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
    
    - name: 设置Python环境  
      uses: actions/setup-python@v4
      with:
        python-version: '3.x'
        
    - name: 安装Python依赖
      run: pip install pandas matplotlib scipy numpy
    
    - name: 下载基准测试结果
      uses: actions/download-artifact@v3
      with:
        name: comprehensive-performance-report-${{ github.sha }}
        path: current-results/
    
    - name: 更新性能基线
      run: |
        echo "更新性能基线数据..."
        
        # 创建基线数据库
        mkdir -p benchmarks/baseline
        
        if [ -f "current-results/all-results.json" ]; then
          # 导入当前结果作为新基线
          python3 scripts/performance_metrics.py import current-results/all-results.json \
            --db benchmarks/baseline/performance.db
          
          # 为每个基准测试设置基线
          python3 -c "
          import json
          import subprocess
          
          with open('current-results/all-results.json') as f:
              results = json.load(f)
          
          for result in results:
              cmd = [
                  'python3', 'scripts/performance_metrics.py', 'baseline',
                  '--db', 'benchmarks/baseline/performance.db',
                  '--crate', result['crate_name'],
                  '--benchmark', result['benchmark_name'],
                  '--duration', str(result['duration_ns']),
                  '--commit', '${{ github.sha }}'
              ]
              subprocess.run(cmd, check=True)
          
          print(f'更新了 {len(results)} 个基准测试的基线')
          "
          
          echo "✅ 性能基线更新完成"
        else
          echo "⚠️ 未找到结果文件，跳过基线更新"
        fi
    
    - name: 提交基线数据
      run: |
        if [ -f "benchmarks/baseline/performance.db" ]; then
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          git add benchmarks/baseline/
          git commit -m "更新性能基线数据 (${{ github.sha }})" || exit 0
          git push
        fi