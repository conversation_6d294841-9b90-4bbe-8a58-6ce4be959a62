{"permissions": {"allow": ["<PERSON><PERSON>(touch:*)", "Bash(cp:*)", "Bash(npm run build:*)", "Bash(cargo check:*)", "Bash(rm:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(cargo test)", "Bash(cargo test:*)", "<PERSON><PERSON>(sed:*)", "Bash(timeout 60 cargo test --test integration_tests)", "Bash(cargo run:*)", "Bash(rustc:*)", "Bash(timeout 60 cargo test -p moduforge-macros-derive)", "Bash(timeout 30 cargo test -p moduforge-macros-derive integration_tests)", "Bash(timeout 30 cargo test -p moduforge-macros-derive)", "Bash(cargo build:*)", "Bash(npm run dev:*)", "Bash(cargo expand:*)", "Bash(.targetdebugexamplesminimal_plugin.exe)", "Bash(targetdebugexamplesminimal_plugin.exe)", "Bash(./target/debug/examples/minimal_plugin.exe)", "WebSearch", "Bash(cargo audit:*)", "Bash(cargo clippy:*)", "Bash(RUST_BACKTRACE=1 cargo test -p moduforge-transform --lib node_step::tests::test_add_node_step)", "<PERSON><PERSON>(chmod:*)", "Bash(cargo bench:*)", "<PERSON><PERSON>(timeout 30 cargo bench -- --test)", "Bash(timeout 30s cargo bench --package moduforge-macros --bench macros)", "Bash(timeout 30 cargo bench -p moduforge-collaboration)", "Bash(timeout 30 cargo bench -p moduforge-rules-template)", "Bash(timeout 30 cargo bench -p moduforge-model --bench model)", "Bash(timeout 30 cargo bench -p moduforge-search --bench search)", "Bash(scriptsstart_benchmarks.bat all)", "<PERSON><PERSON>(timeout:*)", "<PERSON><PERSON>(python:*)", "Bash(chcp:*)", "Bash(PYTHONIOENCODING=utf-8 python scripts/generate_comprehensive_report.py --input benchmarks/results/demo_results.json --output benchmarks/results/report.html --title \"ModuForge-RS Performance Report\" --commit 1a287fe5)", "Bash(start benchmarks/results/report.html)", "Bash(start benchmarks/results/report_local.html)"], "deny": [], "ask": [], "additionalDirectories": ["D:\\mnt\\d\\workspace\\rust2025\\moduforge-rs\\crates", "D:\\d\\workspace\\rust2025\\moduforge-rs\\crates", "D:\\d\\workspace\\rust2025\\moduforge-rs\\packages"]}}