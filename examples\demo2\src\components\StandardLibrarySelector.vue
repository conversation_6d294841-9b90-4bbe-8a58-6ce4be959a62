<template>
  <a-modal
    v-model:open="visible"
    :title="modalTitle"
    width="1000px"
    :footer="null"
    :maskClosable="false"
    @cancel="handleCancel"
  >
    <div class="library-selector">
      <!-- 搜索区域 -->
      <div class="search-area">
        <a-row :gutter="16">
          <a-col :span="8">
            <a-input
              v-model:value="searchForm.keyword"
              placeholder="输入关键词搜索"
              @keyup.enter="handleSearch"
            >
              <template #prefix>
                <SearchOutlined />
              </template>
            </a-input>
          </a-col>
          <a-col :span="6">
            <a-select
              v-model:value="searchForm.category"
              placeholder="选择分类"
              @change="handleSearch"
            >
              <a-select-option value="">全部分类</a-select-option>
              <a-select-option 
                v-for="category in categories" 
                :key="category.value" 
                :value="category.value"
              >
                {{ category.label }}
              </a-select-option>
            </a-select>
          </a-col>
          <a-col :span="6">
            <a-select
              v-model:value="searchForm.unit"
              placeholder="选择单位"
              @change="handleSearch"
            >
              <a-select-option value="">全部单位</a-select-option>
              <a-select-option 
                v-for="unit in units" 
                :key="unit" 
                :value="unit"
              >
                {{ unit }}
              </a-select-option>
            </a-select>
          </a-col>
          <a-col :span="4">
            <a-button type="primary" @click="handleSearch">
              <template #icon><SearchOutlined /></template>
              搜索
            </a-button>
          </a-col>
        </a-row>
      </div>

      <!-- 结果区域 -->
      <div class="results-area">
        <div class="results-header">
          <span>搜索结果 ({{ filteredData.length }} 项)</span>
          <a-space>
            <a-button size="small" @click="refreshData">
              <template #icon><ReloadOutlined /></template>
              刷新
            </a-button>
            <a-button size="small" @click="exportData">
              <template #icon><ExportOutlined /></template>
              导出
            </a-button>
          </a-space>
        </div>

        <a-table
          :columns="tableColumns"
          :data-source="filteredData"
          :pagination="pagination"
          :loading="loading"
          :scroll="{ y: 400 }"
          size="small"
          bordered
          row-key="id"
          :row-selection="rowSelection"
          @change="handleTableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'code'">
              <a-button type="link" @click="selectItem(record)">
                {{ record.code }}
              </a-button>
            </template>
            <template v-else-if="column.key === 'name'">
              <span :title="record.name">{{ record.name }}</span>
            </template>
            <template v-else-if="column.key === 'unitPrice'">
              <span v-if="selectorType === 'quota'">{{ formatCurrency(record.unitPrice) }}</span>
              <span v-else>-</span>
            </template>
            <template v-else-if="column.key === 'actions'">
              <a-space size="small">
                <a-button type="link" size="small" @click="selectItem(record)">
                  选择
                </a-button>
                <a-button type="link" size="small" @click="viewDetail(record)">
                  详情
                </a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </div>

      <!-- 底部按钮 -->
      <div class="footer-actions">
        <a-space>
          <a-button 
            type="primary" 
            @click="confirmSelect"
            :disabled="selectedRowKeys.length === 0"
          >
            确定选择 ({{ selectedRowKeys.length }})
          </a-button>
          <a-button @click="handleCancel">取消</a-button>
        </a-space>
      </div>
    </div>

    <!-- 详情查看弹窗 -->
    <a-modal
      v-model:open="detailVisible"
      title="项目详情"
      width="600px"
      :footer="null"
    >
      <div v-if="currentDetail" class="item-detail">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="编码">{{ currentDetail.code }}</a-descriptions-item>
          <a-descriptions-item label="名称">{{ currentDetail.name }}</a-descriptions-item>
          <a-descriptions-item label="单位">{{ currentDetail.unit }}</a-descriptions-item>
          <a-descriptions-item label="分类">{{ currentDetail.category }}</a-descriptions-item>
          <a-descriptions-item label="单价" v-if="selectorType === 'quota'">
            {{ formatCurrency(currentDetail.unitPrice) }}
          </a-descriptions-item>
          <a-descriptions-item label="更新时间">{{ currentDetail.updateTime }}</a-descriptions-item>
        </a-descriptions>
        
        <div v-if="currentDetail.characteristics" style="margin-top: 16px;">
          <h4>项目特征</h4>
          <p>{{ currentDetail.characteristics }}</p>
        </div>
        
        <div v-if="currentDetail.workContent" style="margin-top: 16px;">
          <h4>工作内容</h4>
          <p>{{ currentDetail.workContent }}</p>
        </div>
        
        <div v-if="selectorType === 'quota' && currentDetail.materials" style="margin-top: 16px;">
          <h4>人材机构成</h4>
          <a-table
            :columns="materialColumns"
            :data-source="currentDetail.materials"
            :pagination="false"
            size="small"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'type'">
                <a-tag :color="getMaterialTypeColor(record.type)">{{ record.type }}</a-tag>
              </template>
            </template>
          </a-table>
        </div>
      </div>
    </a-modal>
  </a-modal>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import {
  SearchOutlined,
  ReloadOutlined,
  ExportOutlined
} from '@ant-design/icons-vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  selectorType: {
    type: String,
    default: 'list', // 'list' | 'quota'
    validator: (value) => ['list', 'quota'].includes(value)
  },
  currentListItem: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:visible', 'select'])

// 状态
const loading = ref(false)
const selectedRowKeys = ref([])
const detailVisible = ref(false)
const currentDetail = ref(null)

// 搜索表单
const searchForm = ref({
  keyword: '',
  category: '',
  unit: ''
})

// 分页配置
const pagination = ref({
  current: 1,
  pageSize: 20,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 项，共 ${total} 项`
})

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const modalTitle = computed(() => {
  return props.selectorType === 'list' ? '清单库选择' : '定额库选择'
})

// 分类选项
const categories = computed(() => {
  if (props.selectorType === 'list') {
    return [
      { value: '0101', label: '土石方工程' },
      { value: '0102', label: '地基处理与边坡支护工程' },
      { value: '0103', label: '桩基工程' },
      { value: '0201', label: '砌筑工程' },
      { value: '0202', label: '混凝土及钢筋混凝土工程' },
      { value: '0203', label: '金属结构工程' }
    ]
  } else {
    return [
      { value: 'A', label: 'A 土石方工程' },
      { value: 'B', label: 'B 地基处理与边坡支护工程' },
      { value: 'C', label: 'C 桩基工程' },
      { value: 'D', label: 'D 砌筑工程' },
      { value: 'E', label: 'E 混凝土及钢筋混凝土工程' }
    ]
  }
})

// 单位选项
const units = ref(['m³', 'm²', 'm', 't', 'kg', '个', '套', '台班', '工日'])

// 表格列定义
const tableColumns = computed(() => {
  const baseColumns = [
    {
      title: '编码',
      dataIndex: 'code',
      key: 'code',
      width: 120,
      ellipsis: true
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      ellipsis: true
    },
    {
      title: '单位',
      dataIndex: 'unit',
      key: 'unit',
      width: 60,
      align: 'center'
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
      width: 100,
      ellipsis: true
    }
  ]

  if (props.selectorType === 'quota') {
    baseColumns.push({
      title: '单价',
      dataIndex: 'unitPrice',
      key: 'unitPrice',
      width: 100,
      align: 'right'
    })
  }

  baseColumns.push({
    title: '操作',
    key: 'actions',
    width: 120,
    align: 'center'
  })

  return baseColumns
})

// 人材机表格列定义
const materialColumns = [
  {
    title: '类型',
    dataIndex: 'type',
    key: 'type',
    width: 60,
    align: 'center'
  },
  {
    title: '名称',
    dataIndex: 'name',
    key: 'name',
    width: 120
  },
  {
    title: '单位',
    dataIndex: 'unit',
    key: 'unit',
    width: 60,
    align: 'center'
  },
  {
    title: '消耗量',
    dataIndex: 'consumption',
    key: 'consumption',
    width: 80,
    align: 'right'
  }
]

// 模拟数据
const mockListData = [
  {
    id: 1,
    code: '010101001',
    name: '平整场地',
    unit: 'm²',
    category: '土石方工程',
    characteristics: '机械平整',
    workContent: '推土机平整、人工修整',
    updateTime: '2024-01-15'
  },
  {
    id: 2,
    code: '010101002',
    name: '挖土方',
    unit: 'm³',
    category: '土石方工程',
    characteristics: '一般土壤',
    workContent: '挖掘机挖土、装车外运',
    updateTime: '2024-01-15'
  }
]

const mockQuotaData = [
  {
    id: 1,
    code: 'A1-1',
    name: '推土机平整场地',
    unit: '100m²',
    category: 'A 土石方工程',
    unitPrice: 85.50,
    characteristics: '功率59kW',
    workContent: '推土机平整场地',
    updateTime: '2024-01-15',
    materials: [
      { type: '人工', name: '普通工', unit: '工日', consumption: 0.1 },
      { type: '机械', name: '推土机', unit: '台班', consumption: 0.05 }
    ]
  },
  {
    id: 2,
    code: 'A1-2',
    name: '人工平整场地',
    unit: 'm²',
    category: 'A 土石方工程',
    unitPrice: 6.50,
    characteristics: '人工精平',
    workContent: '人工平整场地',
    updateTime: '2024-01-15',
    materials: [
      { type: '人工', name: '普通工', unit: '工日', consumption: 0.05 }
    ]
  }
]

// 数据源
const dataSource = computed(() => {
  return props.selectorType === 'list' ? mockListData : mockQuotaData
})

// 过滤后的数据
const filteredData = computed(() => {
  let data = dataSource.value

  // 关键词搜索
  if (searchForm.value.keyword) {
    const keyword = searchForm.value.keyword.toLowerCase()
    data = data.filter(item => 
      item.code.toLowerCase().includes(keyword) ||
      item.name.toLowerCase().includes(keyword)
    )
  }

  // 分类筛选
  if (searchForm.value.category) {
    data = data.filter(item => item.category.includes(searchForm.value.category))
  }

  // 单位筛选
  if (searchForm.value.unit) {
    data = data.filter(item => item.unit === searchForm.value.unit)
  }

  return data
})

// 行选择配置
const rowSelection = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys) => {
    selectedRowKeys.value = keys
  },
  type: 'radio' // 单选
}

// 方法
const formatCurrency = (value) => {
  if (value === null || value === undefined) return ''
  return Number(value).toLocaleString('zh-CN', { 
    minimumFractionDigits: 2,
    maximumFractionDigits: 2 
  })
}

const getMaterialTypeColor = (type) => {
  const colorMap = {
    '人工': 'blue',
    '材料': 'green',
    '机械': 'orange'
  }
  return colorMap[type] || 'default'
}

const handleSearch = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    pagination.value.total = filteredData.value.length
    message.success(`找到 ${filteredData.value.length} 项结果`)
  }, 500)
}

const handleTableChange = (pag, filters, sorter) => {
  pagination.value = { ...pagination.value, ...pag }
}

const selectItem = (record) => {
  selectedRowKeys.value = [record.id]
  confirmSelect()
}

const confirmSelect = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择一项')
    return
  }

  const selectedItem = dataSource.value.find(item => item.id === selectedRowKeys.value[0])
  if (selectedItem) {
    emit('select', selectedItem)
    handleCancel()
    message.success('选择成功')
  }
}

const viewDetail = (record) => {
  currentDetail.value = record
  detailVisible.value = true
}

const refreshData = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    message.success('数据已刷新')
  }, 1000)
}

const exportData = () => {
  message.info('导出功能开发中...')
}

const handleCancel = () => {
  visible.value = false
  selectedRowKeys.value = []
  searchForm.value = {
    keyword: '',
    category: '',
    unit: ''
  }
}

// 监听visible变化，重置状态
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    handleSearch()
  }
})
</script>

<style scoped>
.library-selector {
  display: flex;
  flex-direction: column;
  height: 600px;
}

.search-area {
  padding: 16px;
  background: #fafafa;
  border-bottom: 1px solid #e8e8e8;
}

.results-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.results-header {
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
}

.footer-actions {
  padding: 16px;
  background: #fafafa;
  border-top: 1px solid #e8e8e8;
  text-align: right;
}

.item-detail h4 {
  margin: 16px 0 8px 0;
  color: #262626;
}

.item-detail p {
  margin: 0;
  color: #666;
  line-height: 1.6;
}
</style>
