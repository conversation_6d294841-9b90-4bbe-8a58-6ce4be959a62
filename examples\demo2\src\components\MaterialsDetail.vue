<template>
  <div class="materials-detail">
    <div class="detail-header">
      <div class="selected-item-info">
        <span v-if="selectedRow">
          <strong>{{ selectedRow.name }}</strong> ({{ selectedRow.code }})
        </span>
        <span v-else class="no-selection">请选择一个项目查看人材机明细</span>
      </div>
      <a-space v-if="selectedRow && (selectedRow.type === '定额' || selectedRow.type === '清单')">
        <a-button size="small" @click="addMaterial">
          <template #icon><PlusOutlined /></template>
          添加
        </a-button>
        <a-button size="small" @click="deleteMaterial" :disabled="selectedMaterialKeys.length === 0">
          <template #icon><DeleteOutlined /></template>
          删除
        </a-button>
        <a-button size="small" @click="refreshMaterials">
          <template #icon><ReloadOutlined /></template>
          刷新
        </a-button>
      </a-space>
    </div>

    <div class="detail-content">
      <a-table
        v-if="selectedRow && (selectedRow.type === '定额' || selectedRow.type === '清单')"
        :columns="materialsColumns"
        :data-source="materialsData"
        :pagination="false"
        :scroll="{ y: 120 }"
        size="small"
        bordered
        row-key="id"
        :row-selection="materialRowSelection"
      >
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.key === 'index'">
            {{ index + 1 }}
          </template>
          <template v-else-if="column.key === 'type'">
            <a-tag :color="getMaterialTypeColor(record.type)">{{ record.type }}</a-tag>
          </template>
          <template v-else-if="column.key === 'consumption'">
            <a-input-number
              v-if="editingMaterial.id === record.id && editingMaterial.column === 'consumption'"
              v-model:value="record.consumption"
              :precision="4"
              @blur="finishMaterialEdit"
              @keyup.enter="finishMaterialEdit"
              style="width: 100%;"
            />
            <span v-else @dblclick="startMaterialEdit(record, 'consumption')" class="editable-cell">
              {{ formatNumber(record.consumption) }}
            </span>
          </template>
          <template v-else-if="column.key === 'unitPrice'">
            <a-input-number
              v-if="editingMaterial.id === record.id && editingMaterial.column === 'unitPrice'"
              v-model:value="record.unitPrice"
              :precision="2"
              @blur="finishMaterialEdit"
              @keyup.enter="finishMaterialEdit"
              style="width: 100%;"
            />
            <span v-else @dblclick="startMaterialEdit(record, 'unitPrice')" class="editable-cell">
              {{ formatCurrency(record.unitPrice) }}
            </span>
          </template>
          <template v-else-if="column.key === 'totalPrice'">
            <span class="total-price">{{ formatCurrency(record.totalPrice) }}</span>
          </template>
          <template v-else-if="column.key === 'actions'">
            <a-space size="small">
              <a-button type="link" size="small" @click="editMaterialDetail(record)">
                <template #icon><EditOutlined /></template>
              </a-button>
              <a-button type="link" size="small" @click="viewMaterialHistory(record)">
                <template #icon><HistoryOutlined /></template>
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>

      <div v-else class="empty-state">
        <a-empty description="请选择定额或清单项目查看人材机明细" />
      </div>
    </div>

    <!-- 人材机明细编辑弹窗 -->
    <a-modal
      v-model:open="materialDetailVisible"
      title="人材机明细编辑"
      width="600px"
      @ok="saveMaterialDetail"
    >
      <a-form
        ref="materialFormRef"
        :model="currentMaterial"
        layout="vertical"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="编码" name="code">
              <a-input v-model:value="currentMaterial.code" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="类型" name="type">
              <a-select v-model:value="currentMaterial.type">
                <a-select-option value="人工">人工</a-select-option>
                <a-select-option value="材料">材料</a-select-option>
                <a-select-option value="机械">机械</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-form-item label="名称" name="name">
          <a-input v-model:value="currentMaterial.name" />
        </a-form-item>
        
        <a-form-item label="规格型号" name="specification">
          <a-input v-model:value="currentMaterial.specification" />
        </a-form-item>
        
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="单位" name="unit">
              <a-input v-model:value="currentMaterial.unit" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="消耗量" name="consumption">
              <a-input-number 
                v-model:value="currentMaterial.consumption" 
                :precision="4"
                style="width: 100%;"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="单价" name="unitPrice">
              <a-input-number 
                v-model:value="currentMaterial.unitPrice" 
                :precision="2"
                style="width: 100%;"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-modal>

    <!-- 历史价格查看弹窗 -->
    <a-modal
      v-model:open="historyVisible"
      title="历史价格"
      width="800px"
      :footer="null"
    >
      <a-table
        :columns="historyColumns"
        :data-source="historyData"
        :pagination="false"
        size="small"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'trend'">
            <span :class="['trend', record.trend]">
              {{ record.trend === 'up' ? '↑' : record.trend === 'down' ? '↓' : '→' }}
              {{ record.changeRate }}%
            </span>
          </template>
        </template>
      </a-table>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  DeleteOutlined,
  ReloadOutlined,
  EditOutlined,
  HistoryOutlined
} from '@ant-design/icons-vue'

const props = defineProps({
  selectedRow: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update'])

// 状态
const selectedMaterialKeys = ref([])
const editingMaterial = ref({ id: null, column: null })
const materialDetailVisible = ref(false)
const historyVisible = ref(false)
const currentMaterial = ref({})
const materialFormRef = ref()

// 人材机表格列定义
const materialsColumns = [
  {
    title: '#',
    key: 'index',
    width: 50,
    align: 'center'
  },
  {
    title: '编码',
    dataIndex: 'code',
    key: 'code',
    width: 100
  },
  {
    title: '类型',
    dataIndex: 'type',
    key: 'type',
    width: 60,
    align: 'center'
  },
  {
    title: '名称',
    dataIndex: 'name',
    key: 'name',
    width: 120,
    ellipsis: true
  },
  {
    title: '规格型号',
    dataIndex: 'specification',
    key: 'specification',
    width: 100,
    ellipsis: true
  },
  {
    title: '单位',
    dataIndex: 'unit',
    key: 'unit',
    width: 50,
    align: 'center'
  },
  {
    title: '消耗量',
    dataIndex: 'consumption',
    key: 'consumption',
    width: 80,
    align: 'right'
  },
  {
    title: '单价',
    dataIndex: 'unitPrice',
    key: 'unitPrice',
    width: 80,
    align: 'right'
  },
  {
    title: '合价',
    dataIndex: 'totalPrice',
    key: 'totalPrice',
    width: 80,
    align: 'right'
  },
  {
    title: '操作',
    key: 'actions',
    width: 80,
    align: 'center'
  }
]

// 历史价格表格列定义
const historyColumns = [
  {
    title: '时间',
    dataIndex: 'date',
    key: 'date',
    width: 100
  },
  {
    title: '单价',
    dataIndex: 'price',
    key: 'price',
    width: 80,
    align: 'right'
  },
  {
    title: '变化趋势',
    key: 'trend',
    width: 80,
    align: 'center'
  },
  {
    title: '来源',
    dataIndex: 'source',
    key: 'source',
    width: 120
  }
]

// 模拟人材机数据
const materialsData = ref([
  {
    id: 1,
    code: 'RG001',
    type: '人工',
    name: '普通工',
    specification: '',
    unit: '工日',
    consumption: 0.5,
    unitPrice: 120,
    totalPrice: 60
  },
  {
    id: 2,
    code: 'CL001',
    type: '材料',
    name: '水泥',
    specification: 'P.O 42.5',
    unit: 't',
    consumption: 0.35,
    unitPrice: 450,
    totalPrice: 157.5
  },
  {
    id: 3,
    code: 'JX001',
    type: '机械',
    name: '推土机',
    specification: '功率59kW',
    unit: '台班',
    consumption: 0.1,
    unitPrice: 800,
    totalPrice: 80
  }
])

// 历史价格数据
const historyData = ref([
  {
    date: '2024-01',
    price: 450,
    trend: 'up',
    changeRate: 5.2,
    source: '市场价'
  },
  {
    date: '2023-12',
    price: 428,
    trend: 'down',
    changeRate: -2.1,
    source: '市场价'
  },
  {
    date: '2023-11',
    price: 437,
    trend: 'up',
    changeRate: 1.8,
    source: '市场价'
  }
])

// 行选择配置
const materialRowSelection = {
  selectedRowKeys: selectedMaterialKeys,
  onChange: (keys) => {
    selectedMaterialKeys.value = keys
  }
}

// 方法
const getMaterialTypeColor = (type) => {
  const colorMap = {
    '人工': 'blue',
    '材料': 'green',
    '机械': 'orange'
  }
  return colorMap[type] || 'default'
}

const formatNumber = (value) => {
  if (value === null || value === undefined) return ''
  return Number(value).toLocaleString('zh-CN', { 
    minimumFractionDigits: 0,
    maximumFractionDigits: 4 
  })
}

const formatCurrency = (value) => {
  if (value === null || value === undefined) return ''
  return Number(value).toLocaleString('zh-CN', { 
    minimumFractionDigits: 2,
    maximumFractionDigits: 2 
  })
}

const startMaterialEdit = (record, column) => {
  editingMaterial.value = { id: record.id, column }
}

const finishMaterialEdit = () => {
  editingMaterial.value = { id: null, column: null }
  calculateMaterialTotals()
  emit('update', materialsData.value)
}

const calculateMaterialTotals = () => {
  materialsData.value.forEach(material => {
    material.totalPrice = (material.consumption || 0) * (material.unitPrice || 0)
  })
}

const addMaterial = () => {
  const newMaterial = {
    id: Date.now(),
    code: '',
    type: '材料',
    name: '',
    specification: '',
    unit: '',
    consumption: 0,
    unitPrice: 0,
    totalPrice: 0
  }
  materialsData.value.push(newMaterial)
  emit('update', materialsData.value)
}

const deleteMaterial = () => {
  if (selectedMaterialKeys.value.length === 0) return
  
  materialsData.value = materialsData.value.filter(
    item => !selectedMaterialKeys.value.includes(item.id)
  )
  selectedMaterialKeys.value = []
  emit('update', materialsData.value)
  message.success('删除成功')
}

const refreshMaterials = () => {
  message.info('刷新人材机数据...')
  // TODO: 实现数据刷新逻辑
}

const editMaterialDetail = (record) => {
  currentMaterial.value = { ...record }
  materialDetailVisible.value = true
}

const saveMaterialDetail = () => {
  const index = materialsData.value.findIndex(item => item.id === currentMaterial.value.id)
  if (index !== -1) {
    materialsData.value[index] = { ...currentMaterial.value }
    calculateMaterialTotals()
    emit('update', materialsData.value)
  }
  materialDetailVisible.value = false
  message.success('保存成功')
}

const viewMaterialHistory = (record) => {
  currentMaterial.value = record
  historyVisible.value = true
}

// 监听选中行变化
watch(() => props.selectedRow, (newRow) => {
  if (newRow && (newRow.type === '定额' || newRow.type === '清单')) {
    // 根据选中行加载对应的人材机数据
    // TODO: 实现数据加载逻辑
  }
}, { immediate: true })
</script>

<style scoped>
.materials-detail {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.detail-header {
  padding: 12px 16px;
  background: #fafafa;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.selected-item-info {
  font-size: 14px;
}

.no-selection {
  color: #999;
}

.detail-content {
  flex: 1;
  padding: 8px;
  overflow: hidden;
}

.empty-state {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.editable-cell {
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.editable-cell:hover {
  background-color: #f5f5f5;
}

.total-price {
  font-weight: 500;
  color: #262626;
}

.trend {
  font-weight: 500;
}

.trend.up {
  color: #ff4d4f;
}

.trend.down {
  color: #52c41a;
}

.trend.stable {
  color: #999;
}
</style>
