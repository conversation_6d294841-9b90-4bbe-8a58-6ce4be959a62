<template>
  <div class="subdivision-editor">
    <!-- 工具栏 -->
    <div class="editor-toolbar">
      <a-space>
        <a-button type="primary" @click="addRow">
          <template #icon><PlusOutlined /></template>
          新增
        </a-button>
        <a-button @click="insertRow" :disabled="selectedRowKeys.length === 0">
          <template #icon><InsertRowAboveOutlined /></template>
          插入
        </a-button>
        <a-button danger @click="deleteSelected" :disabled="selectedRowKeys.length === 0">
          <template #icon><DeleteOutlined /></template>
          删除
        </a-button>
        <a-divider type="vertical" />
        <a-button @click="copyRows" :disabled="selectedRowKeys.length === 0">
          <template #icon><CopyOutlined /></template>
          复制
        </a-button>
        <a-button @click="pasteRows" :disabled="!canPaste">
          <template #icon><SnippetsOutlined /></template>
          粘贴
        </a-button>
        <a-divider type="vertical" />
        <a-button @click="moveUp" :disabled="selectedRowKeys.length === 0">
          <template #icon><ArrowUpOutlined /></template>
          上移
        </a-button>
        <a-button @click="moveDown" :disabled="selectedRowKeys.length === 0">
          <template #icon><ArrowDownOutlined /></template>
          下移
        </a-button>
      </a-space>
    </div>

    <!-- 分部分项表格 -->
    <div class="table-container">
      <a-table
        ref="tableRef"
        :columns="tableColumns"
        :data-source="filteredData"
        :pagination="false"
        :loading="loading"
        :scroll="{ x: 1200, y: 400 }"
        size="middle"
        bordered
        row-key="id"
        :row-selection="rowSelection"
        @change="handleTableChange"
      >
        <!-- 自定义列渲染 -->
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.key === 'rowNumber'">
            {{ index + 1 }}
          </template>
          <template v-else-if="column.key === 'code'">
            <a-input
              v-if="editingCell.rowId === record.id && editingCell.column === 'code'"
              v-model:value="record.code"
              @blur="finishEdit"
              @keyup.enter="finishEdit"
              @keyup.escape="cancelEdit"
              ref="editInput"
            />
            <span v-else @dblclick="startEdit(record, 'code')" class="editable-cell">
              {{ record.code }}
            </span>
          </template>
          <template v-else-if="column.key === 'type'">
            <a-tag :color="getTypeColor(record.type)">{{ record.type }}</a-tag>
          </template>
          <template v-else-if="column.key === 'name'">
            <a-input
              v-if="editingCell.rowId === record.id && editingCell.column === 'name'"
              v-model:value="record.name"
              @blur="finishEdit"
              @keyup.enter="finishEdit"
              @keyup.escape="cancelEdit"
              ref="editInput"
            />
            <span v-else @dblclick="startEdit(record, 'name')" class="editable-cell">
              {{ record.name }}
            </span>
          </template>
          <template v-else-if="column.key === 'characteristics'">
            <a-input
              v-if="editingCell.rowId === record.id && editingCell.column === 'characteristics'"
              v-model:value="record.characteristics"
              @blur="finishEdit"
              @keyup.enter="finishEdit"
              @keyup.escape="cancelEdit"
              ref="editInput"
            />
            <span v-else @dblclick="startEdit(record, 'characteristics')" class="editable-cell">
              {{ record.characteristics }}
            </span>
          </template>
          <template v-else-if="column.key === 'unit'">
            <span>{{ record.unit }}</span>
          </template>
          <template v-else-if="column.key === 'quantity'">
            <a-input-number
              v-if="editingCell.rowId === record.id && editingCell.column === 'quantity'"
              v-model:value="record.quantity"
              :precision="4"
              @blur="finishEdit"
              @keyup.enter="finishEdit"
              @keyup.escape="cancelEdit"
              ref="editInput"
              style="width: 100%;"
            />
            <span v-else @dblclick="startEdit(record, 'quantity')" class="editable-cell quantity-cell">
              {{ formatNumber(record.quantity) }}
              <CalculatorOutlined 
                v-if="record.type === '清单' || record.type === '定额'"
                class="calculator-icon" 
                @click="openCalculator(record)"
              />
            </span>
          </template>
          <template v-else-if="column.key === 'unitPrice'">
            <a-input-number
              v-if="editingCell.rowId === record.id && editingCell.column === 'unitPrice'"
              v-model:value="record.unitPrice"
              :precision="2"
              @blur="finishEdit"
              @keyup.enter="finishEdit"
              @keyup.escape="cancelEdit"
              ref="editInput"
              style="width: 100%;"
            />
            <span v-else @dblclick="startEdit(record, 'unitPrice')" class="editable-cell">
              {{ record.type === '单位' || record.type === '分部' ? '汇总' : formatCurrency(record.unitPrice) }}
            </span>
          </template>
          <template v-else-if="column.key === 'totalPrice'">
            <span class="total-price">
              {{ record.type === '单位' || record.type === '分部' ? '汇总' : formatCurrency(record.totalPrice) }}
            </span>
          </template>
        </template>
      </a-table>
    </div>

    <!-- 人材机明细区域 -->
    <div class="details-area">
      <a-tabs v-model:activeKey="detailsTabKey" size="small">
        <a-tab-pane key="materials" tab="人材机明细">
          <MaterialsDetail 
            :selected-row="selectedDetailRow"
            @update="onMaterialsUpdate"
          />
        </a-tab-pane>
        <a-tab-pane key="composition" tab="单价构成">
          <PriceComposition 
            :selected-row="selectedDetailRow"
          />
        </a-tab-pane>
      </a-tabs>
    </div>

    <!-- 标准库选择器 -->
    <StandardLibrarySelector
      v-model:visible="libraryVisible"
      :selector-type="libraryType"
      :current-list-item="currentListItem"
      @select="onStandardSelect"
    />

    <!-- 工程量计算器 -->
    <QuantityCalculator
      v-model:visible="calculatorVisible"
      :current-item="calculatorItem"
      @calculate="onQuantityCalculate"
    />
  </div>
</template>

<script setup>
import { ref, computed, nextTick, watch } from 'vue'
import { message } from 'ant-design-vue'
// import { STable } from '@cost-app/shared-components' // 使用标准 a-table 替代
import MaterialsDetail from './MaterialsDetail.vue'
import PriceComposition from './PriceComposition.vue'
import StandardLibrarySelector from './StandardLibrarySelector.vue'
import QuantityCalculator from './QuantityCalculator.vue'
import {
  PlusOutlined,
  DeleteOutlined,
  CopyOutlined,
  SnippetsOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  InsertRowAboveOutlined,
  CalculatorOutlined
} from '@ant-design/icons-vue'

const props = defineProps({
  projectData: {
    type: Object,
    default: () => ({})
  },
  selectedUnit: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['data-change'])

// 表格状态
const loading = ref(false)
const selectedRowKeys = ref([])
const editingCell = ref({ rowId: null, column: null })
const tableRef = ref()
const editInput = ref()

// 明细区域状态
const detailsTabKey = ref('materials')
const selectedDetailRow = ref(null)

// 弹窗状态
const libraryVisible = ref(false)
const libraryType = ref('list') // 'list' | 'quota'
const currentListItem = ref(null)
const calculatorVisible = ref(false)
const calculatorItem = ref(null)

// 剪贴板
const clipboard = ref([])
const canPaste = computed(() => clipboard.value.length > 0)

// 表格列定义
const tableColumns = [
  {
    title: '#',
    key: 'rowNumber',
    width: 50,
    align: 'center'
  },
  {
    title: '编码',
    key: 'code',
    width: 120,
    ellipsis: true
  },
  {
    title: '类型',
    key: 'type',
    width: 80,
    align: 'center'
  },
  {
    title: '项目名称',
    key: 'name',
    width: 200,
    ellipsis: true
  },
  {
    title: '项目特征',
    key: 'characteristics',
    width: 150,
    ellipsis: true
  },
  {
    title: '单位',
    key: 'unit',
    width: 60,
    align: 'center'
  },
  {
    title: '工程量',
    key: 'quantity',
    width: 120,
    align: 'right'
  },
  {
    title: '单价',
    key: 'unitPrice',
    width: 120,
    align: 'right'
  },
  {
    title: '合价',
    key: 'totalPrice',
    width: 120,
    align: 'right'
  }
]

// 模拟数据
const tableData = ref([
  {
    id: 1,
    code: '',
    type: '单位',
    name: '住宅楼工程',
    characteristics: '5层砖混',
    unit: '',
    quantity: null,
    unitPrice: null,
    totalPrice: 12000000,
    level: 0
  },
  {
    id: 2,
    code: '0101',
    type: '分部',
    name: '土石方工程',
    characteristics: '',
    unit: '',
    quantity: null,
    unitPrice: null,
    totalPrice: 102000,
    level: 1
  },
  {
    id: 3,
    code: '010101',
    type: '清单',
    name: '平整场地',
    characteristics: '机械平整',
    unit: 'm²',
    quantity: 1200,
    unitPrice: 8.5,
    totalPrice: 10200,
    level: 2
  },
  {
    id: 4,
    code: 'A1-1',
    type: '定额',
    name: '推土机平整',
    characteristics: '功率59kW',
    unit: '100m²',
    quantity: 12,
    unitPrice: 85,
    totalPrice: 1020,
    level: 3
  },
  {
    id: 5,
    code: 'A1-2',
    type: '定额',
    name: '人工修整',
    characteristics: '人工精平',
    unit: 'm²',
    quantity: 1200,
    unitPrice: 6.5,
    totalPrice: 7800,
    level: 3
  }
])

const filteredData = computed(() => tableData.value)

// 行选择配置
const rowSelection = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys, rows) => {
    selectedRowKeys.value = keys
    selectedDetailRow.value = rows[0] || null
  },
  getCheckboxProps: (record) => ({
    disabled: record.type === '单位' // 单位工程行不可选择
  })
}

// 方法
const getTypeColor = (type) => {
  const colorMap = {
    '单位': 'blue',
    '分部': 'green',
    '清单': 'orange',
    '定额': 'purple'
  }
  return colorMap[type] || 'default'
}

const formatNumber = (value) => {
  if (value === null || value === undefined) return ''
  return Number(value).toLocaleString('zh-CN', { 
    minimumFractionDigits: 0,
    maximumFractionDigits: 4 
  })
}

const formatCurrency = (value) => {
  if (value === null || value === undefined) return ''
  return Number(value).toLocaleString('zh-CN', { 
    minimumFractionDigits: 2,
    maximumFractionDigits: 2 
  })
}

const startEdit = async (record, column) => {
  if (record.type === '单位' || record.type === '分部') {
    if (column === 'quantity' || column === 'unitPrice') {
      message.warning('汇总行不可编辑')
      return
    }
  }
  
  editingCell.value = { rowId: record.id, column }
  await nextTick()
  if (editInput.value) {
    editInput.value.focus()
  }
}

const finishEdit = () => {
  editingCell.value = { rowId: null, column: null }
  calculateTotals()
  emit('data-change', tableData.value)
}

const cancelEdit = () => {
  editingCell.value = { rowId: null, column: null }
}

const calculateTotals = () => {
  // 计算合价
  tableData.value.forEach(row => {
    if (row.type === '清单' || row.type === '定额') {
      row.totalPrice = (row.quantity || 0) * (row.unitPrice || 0)
    }
  })
}

const onCellClick = (record, column) => {
  // 处理单元格点击
}

const onCellDoubleClick = (record, column) => {
  if (column.key === 'code') {
    if (record.type === '清单') {
      openStandardLibrary('list', record)
    } else if (record.type === '定额') {
      openStandardLibrary('quota', record)
    }
  } else if (column.key === 'quantity') {
    openCalculator(record)
  }
}

const openStandardLibrary = (type, record) => {
  libraryType.value = type
  currentListItem.value = record
  libraryVisible.value = true
}

const openCalculator = (record) => {
  if (record.type === '清单' || record.type === '定额') {
    calculatorItem.value = record
    calculatorVisible.value = true
  }
}

const onStandardSelect = (selectedItem) => {
  if (currentListItem.value) {
    currentListItem.value.code = selectedItem.code
    currentListItem.value.name = selectedItem.name
    currentListItem.value.unit = selectedItem.unit
    currentListItem.value.characteristics = selectedItem.characteristics || ''
    
    if (libraryType.value === 'quota') {
      // 定额选择后，插入定额行
      insertQuotaRow(selectedItem)
    }
    
    calculateTotals()
    emit('data-change', tableData.value)
  }
}

const onQuantityCalculate = (result) => {
  if (calculatorItem.value) {
    calculatorItem.value.quantity = result.quantity
    calculatorItem.value.formula = result.formula
    calculateTotals()
    emit('data-change', tableData.value)
  }
}

const insertQuotaRow = (quotaItem) => {
  // 在当前清单项下插入定额行
  const currentIndex = tableData.value.findIndex(item => item.id === currentListItem.value.id)
  if (currentIndex !== -1) {
    const newQuotaRow = {
      id: Date.now(),
      code: quotaItem.code,
      type: '定额',
      name: quotaItem.name,
      characteristics: quotaItem.characteristics || '',
      unit: quotaItem.unit,
      quantity: 0,
      unitPrice: quotaItem.unitPrice || 0,
      totalPrice: 0,
      level: 3
    }
    tableData.value.splice(currentIndex + 1, 0, newQuotaRow)
  }
}

const addRow = () => {
  const newRow = {
    id: Date.now(),
    code: '',
    type: '清单',
    name: '',
    characteristics: '',
    unit: '',
    quantity: 0,
    unitPrice: 0,
    totalPrice: 0,
    level: 2
  }
  tableData.value.push(newRow)
  emit('data-change', tableData.value)
}

const insertRow = () => {
  if (selectedRowKeys.value.length === 0) return
  
  const selectedIndex = tableData.value.findIndex(item => item.id === selectedRowKeys.value[0])
  if (selectedIndex !== -1) {
    const newRow = {
      id: Date.now(),
      code: '',
      type: '清单',
      name: '',
      characteristics: '',
      unit: '',
      quantity: 0,
      unitPrice: 0,
      totalPrice: 0,
      level: 2
    }
    tableData.value.splice(selectedIndex, 0, newRow)
    emit('data-change', tableData.value)
  }
}

const deleteSelected = () => {
  if (selectedRowKeys.value.length === 0) return
  
  const hasUnitRow = selectedRowKeys.value.some(key => {
    const row = tableData.value.find(item => item.id === key)
    return row && row.type === '单位'
  })
  
  if (hasUnitRow) {
    message.warning('单位工程行不可删除')
    return
  }
  
  tableData.value = tableData.value.filter(item => !selectedRowKeys.value.includes(item.id))
  selectedRowKeys.value = []
  selectedDetailRow.value = null
  emit('data-change', tableData.value)
  message.success(`已删除 ${selectedRowKeys.value.length} 行`)
}

const copyRows = () => {
  const selectedRows = tableData.value.filter(item => selectedRowKeys.value.includes(item.id))
  clipboard.value = selectedRows.map(row => ({ ...row, id: null }))
  message.success(`已复制 ${selectedRows.length} 行`)
}

const pasteRows = () => {
  if (clipboard.value.length === 0) return
  
  const newRows = clipboard.value.map(row => ({
    ...row,
    id: Date.now() + Math.random()
  }))
  
  tableData.value.push(...newRows)
  emit('data-change', tableData.value)
  message.success(`已粘贴 ${newRows.length} 行`)
}

const moveUp = () => {
  // TODO: 实现上移功能
  message.info('上移功能开发中...')
}

const moveDown = () => {
  // TODO: 实现下移功能
  message.info('下移功能开发中...')
}

const handleTableChange = (pagination, filters, sorter) => {
  // 处理表格变化
}

const onMaterialsUpdate = (data) => {
  // 处理人材机明细更新
  calculateTotals()
  emit('data-change', tableData.value)
}

// 监听选中单位工程变化
watch(() => props.selectedUnit, (newUnit) => {
  if (newUnit) {
    // 根据选中的单位工程加载对应数据
    // TODO: 实现数据加载逻辑
  }
}, { immediate: true })
</script>

<style scoped>
.subdivision-editor {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.editor-toolbar {
  padding: 12px 16px;
  background: #fafafa;
  border-bottom: 1px solid #e8e8e8;
}

.table-container {
  flex: 1;
  min-height: 0;
  padding: 16px;
}

.details-area {
  height: 200px;
  border-top: 1px solid #e8e8e8;
  background: white;
}

.editable-cell {
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.editable-cell:hover {
  background-color: #f5f5f5;
}

.quantity-cell {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.calculator-icon {
  color: #1890ff;
  cursor: pointer;
  margin-left: 8px;
}

.calculator-icon:hover {
  color: #40a9ff;
}

.total-price {
  font-weight: 500;
  color: #262626;
}

/* 表格样式覆盖 */
:deep(.ant-table) {
  .ant-table-tbody > tr > td {
    padding: 8px;
  }

  .ant-table-tbody > tr:hover > td {
    background-color: #f5f5f5;
  }

  .ant-table-tbody > tr.ant-table-row-selected > td {
    background-color: #e6f7ff;
  }
}
</style>
