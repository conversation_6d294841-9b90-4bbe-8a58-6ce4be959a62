<template>
  <div class="price-composition">
    <div class="composition-header">
      <div class="selected-item-info">
        <span v-if="selectedRow">
          <strong>{{ selectedRow.name }}</strong> 单价构成分析
        </span>
        <span v-else class="no-selection">请选择一个项目查看单价构成</span>
      </div>
      <a-space v-if="selectedRow">
        <a-button size="small" @click="refreshComposition">
          <template #icon><ReloadOutlined /></template>
          刷新
        </a-button>
        <a-button size="small" @click="exportComposition">
          <template #icon><ExportOutlined /></template>
          导出
        </a-button>
      </a-space>
    </div>

    <div class="composition-content">
      <div v-if="selectedRow" class="composition-analysis">
        <!-- 单价汇总 -->
        <a-card size="small" title="单价汇总" class="summary-card">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-statistic 
                title="人工费" 
                :value="compositionData.laborCost" 
                suffix="元"
                :precision="2"
              />
            </a-col>
            <a-col :span="6">
              <a-statistic 
                title="材料费" 
                :value="compositionData.materialCost" 
                suffix="元"
                :precision="2"
              />
            </a-col>
            <a-col :span="6">
              <a-statistic 
                title="机械费" 
                :value="compositionData.machineryCost" 
                suffix="元"
                :precision="2"
              />
            </a-col>
            <a-col :span="6">
              <a-statistic 
                title="合计" 
                :value="compositionData.totalCost" 
                suffix="元"
                :precision="2"
                :value-style="{ color: '#cf1322' }"
              />
            </a-col>
          </a-row>
        </a-card>

        <!-- 费用构成饼图 -->
        <a-card size="small" title="费用构成" class="chart-card">
          <div class="chart-container">
            <div class="pie-chart">
              <div class="chart-legend">
                <div class="legend-item">
                  <span class="legend-color labor"></span>
                  <span>人工费 {{ getPercentage(compositionData.laborCost) }}%</span>
                </div>
                <div class="legend-item">
                  <span class="legend-color material"></span>
                  <span>材料费 {{ getPercentage(compositionData.materialCost) }}%</span>
                </div>
                <div class="legend-item">
                  <span class="legend-color machinery"></span>
                  <span>机械费 {{ getPercentage(compositionData.machineryCost) }}%</span>
                </div>
              </div>
              <!-- 这里可以集成真实的图表组件，如 ECharts -->
              <div class="simple-pie">
                <div 
                  class="pie-segment labor" 
                  :style="{ 
                    '--angle': `${getPercentage(compositionData.laborCost) * 3.6}deg`,
                    '--start': '0deg'
                  }"
                ></div>
                <div 
                  class="pie-segment material" 
                  :style="{ 
                    '--angle': `${getPercentage(compositionData.materialCost) * 3.6}deg`,
                    '--start': `${getPercentage(compositionData.laborCost) * 3.6}deg`
                  }"
                ></div>
                <div 
                  class="pie-segment machinery" 
                  :style="{ 
                    '--angle': `${getPercentage(compositionData.machineryCost) * 3.6}deg`,
                    '--start': `${(getPercentage(compositionData.laborCost) + getPercentage(compositionData.materialCost)) * 3.6}deg`
                  }"
                ></div>
              </div>
            </div>
          </div>
        </a-card>

        <!-- 详细构成表格 -->
        <a-card size="small" title="详细构成" class="detail-table-card">
          <a-table
            :columns="compositionColumns"
            :data-source="compositionDetails"
            :pagination="false"
            :scroll="{ y: 200 }"
            size="small"
            bordered
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'type'">
                <a-tag :color="getTypeColor(record.type)">{{ record.type }}</a-tag>
              </template>
              <template v-else-if="column.key === 'consumption'">
                {{ formatNumber(record.consumption) }}
              </template>
              <template v-else-if="column.key === 'unitPrice'">
                {{ formatCurrency(record.unitPrice) }}
              </template>
              <template v-else-if="column.key === 'totalPrice'">
                <span class="total-price">{{ formatCurrency(record.totalPrice) }}</span>
              </template>
              <template v-else-if="column.key === 'percentage'">
                <span class="percentage">{{ getItemPercentage(record.totalPrice) }}%</span>
              </template>
            </template>
          </a-table>
        </a-card>

        <!-- 费率分析 -->
        <a-card size="small" title="费率分析" class="rate-analysis-card">
          <a-table
            :columns="rateColumns"
            :data-source="rateAnalysis"
            :pagination="false"
            size="small"
            bordered
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'rate'">
                {{ record.rate }}%
              </template>
              <template v-else-if="column.key === 'amount'">
                {{ formatCurrency(record.amount) }}
              </template>
            </template>
          </a-table>
        </a-card>
      </div>

      <div v-else class="empty-state">
        <a-empty description="请选择项目查看单价构成分析" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { message } from 'ant-design-vue'
import {
  ReloadOutlined,
  ExportOutlined
} from '@ant-design/icons-vue'

const props = defineProps({
  selectedRow: {
    type: Object,
    default: null
  }
})

// 单价构成数据
const compositionData = ref({
  laborCost: 60,      // 人工费
  materialCost: 157.5, // 材料费
  machineryCost: 80,   // 机械费
  totalCost: 297.5     // 合计
})

// 详细构成表格列定义
const compositionColumns = [
  {
    title: '类型',
    dataIndex: 'type',
    key: 'type',
    width: 80,
    align: 'center'
  },
  {
    title: '名称',
    dataIndex: 'name',
    key: 'name',
    width: 120,
    ellipsis: true
  },
  {
    title: '规格型号',
    dataIndex: 'specification',
    key: 'specification',
    width: 100,
    ellipsis: true
  },
  {
    title: '单位',
    dataIndex: 'unit',
    key: 'unit',
    width: 60,
    align: 'center'
  },
  {
    title: '消耗量',
    dataIndex: 'consumption',
    key: 'consumption',
    width: 80,
    align: 'right'
  },
  {
    title: '单价',
    dataIndex: 'unitPrice',
    key: 'unitPrice',
    width: 80,
    align: 'right'
  },
  {
    title: '合价',
    dataIndex: 'totalPrice',
    key: 'totalPrice',
    width: 80,
    align: 'right'
  },
  {
    title: '占比',
    key: 'percentage',
    width: 60,
    align: 'right'
  }
]

// 费率分析表格列定义
const rateColumns = [
  {
    title: '费用项目',
    dataIndex: 'name',
    key: 'name',
    width: 120
  },
  {
    title: '费率',
    dataIndex: 'rate',
    key: 'rate',
    width: 80,
    align: 'right'
  },
  {
    title: '金额',
    dataIndex: 'amount',
    key: 'amount',
    width: 80,
    align: 'right'
  },
  {
    title: '说明',
    dataIndex: 'description',
    key: 'description',
    ellipsis: true
  }
]

// 详细构成数据
const compositionDetails = ref([
  {
    id: 1,
    type: '人工',
    name: '普通工',
    specification: '',
    unit: '工日',
    consumption: 0.5,
    unitPrice: 120,
    totalPrice: 60
  },
  {
    id: 2,
    type: '材料',
    name: '水泥',
    specification: 'P.O 42.5',
    unit: 't',
    consumption: 0.35,
    unitPrice: 450,
    totalPrice: 157.5
  },
  {
    id: 3,
    type: '机械',
    name: '推土机',
    specification: '功率59kW',
    unit: '台班',
    consumption: 0.1,
    unitPrice: 800,
    totalPrice: 80
  }
])

// 费率分析数据
const rateAnalysis = ref([
  {
    name: '企业管理费',
    rate: 8.5,
    amount: 25.29,
    description: '按人工费+机械费计取'
  },
  {
    name: '利润',
    rate: 7.0,
    amount: 20.83,
    description: '按人工费+机械费计取'
  },
  {
    name: '规费',
    rate: 3.2,
    amount: 9.52,
    description: '按人工费计取'
  },
  {
    name: '税金',
    rate: 9.0,
    amount: 31.77,
    description: '按含税造价计取'
  }
])

// 方法
const getTypeColor = (type) => {
  const colorMap = {
    '人工': 'blue',
    '材料': 'green',
    '机械': 'orange'
  }
  return colorMap[type] || 'default'
}

const formatNumber = (value) => {
  if (value === null || value === undefined) return ''
  return Number(value).toLocaleString('zh-CN', { 
    minimumFractionDigits: 0,
    maximumFractionDigits: 4 
  })
}

const formatCurrency = (value) => {
  if (value === null || value === undefined) return ''
  return Number(value).toLocaleString('zh-CN', { 
    minimumFractionDigits: 2,
    maximumFractionDigits: 2 
  })
}

const getPercentage = (value) => {
  if (!compositionData.value.totalCost) return 0
  return ((value / compositionData.value.totalCost) * 100).toFixed(1)
}

const getItemPercentage = (value) => {
  if (!compositionData.value.totalCost) return 0
  return ((value / compositionData.value.totalCost) * 100).toFixed(1)
}

const refreshComposition = () => {
  message.info('刷新单价构成数据...')
  // TODO: 实现数据刷新逻辑
}

const exportComposition = () => {
  message.info('导出单价构成分析...')
  // TODO: 实现导出功能
}

// 监听选中行变化
watch(() => props.selectedRow, (newRow) => {
  if (newRow && (newRow.type === '定额' || newRow.type === '清单')) {
    // 根据选中行加载对应的单价构成数据
    // TODO: 实现数据加载逻辑
  }
}, { immediate: true })
</script>

<style scoped>
.price-composition {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.composition-header {
  padding: 12px 16px;
  background: #fafafa;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.selected-item-info {
  font-size: 14px;
}

.no-selection {
  color: #999;
}

.composition-content {
  flex: 1;
  padding: 8px;
  overflow-y: auto;
}

.composition-analysis {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.summary-card {
  margin-bottom: 0;
}

.chart-card {
  margin-bottom: 0;
}

.chart-container {
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pie-chart {
  display: flex;
  align-items: center;
  gap: 20px;
}

.simple-pie {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  position: relative;
  background: conic-gradient(
    #1890ff 0deg 72deg,
    #52c41a 72deg 185deg,
    #faad14 185deg 360deg
  );
}

.chart-legend {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-color.labor {
  background-color: #1890ff;
}

.legend-color.material {
  background-color: #52c41a;
}

.legend-color.machinery {
  background-color: #faad14;
}

.detail-table-card,
.rate-analysis-card {
  margin-bottom: 0;
}

.empty-state {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.total-price {
  font-weight: 500;
  color: #262626;
}

.percentage {
  color: #666;
  font-size: 12px;
}
</style>
